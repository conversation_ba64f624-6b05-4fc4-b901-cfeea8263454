<?php
namespace App\Exports;

use App\Models\Cashier_Report;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use Illuminate\Contracts\Support\Responsable;

class CashierCustomExport implements Responsable
{
    public $fileName = 'Central_Closing.xlsx';
    private $date, $location;

    public function __construct($date, $location)
    {
        $this->date = $date;
        $this->location = $location;
    }

    public function toResponse($request)
    {
        $templatePath = storage_path('app/excel/Central_Closing.xlsx'); // original file
        $exportPath = storage_path("app/excel/{$this->fileName}");

        $spreadsheet = IOFactory::load($templatePath);
        $sheet = $spreadsheet->getActiveSheet();

        $sheet->setCellValue('K2', date('d/m/Y', strtotime($this->date)));
        $sheet->setCellValue('F2', $this->location); // Optional

        $reports = Cashier_Report::where('cashbox_date', $this->date)
            ->where('location_id', $this->location)
            ->get();

        $row = 5;
        foreach ($reports as $report) {
            $sheet->setCellValue("C{$row}", $report->user->username);
            $sheet->setCellValue("D{$row}", $report->location->name_ar);
            $row++;
        }

        (new Xlsx($spreadsheet))->save($exportPath);

        return response()->download($exportPath);
    }
}