<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\roles;
use App\Models\library;
use App\Models\roles_permisstions;

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use App\Models\locations;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules;
use Illuminate\Validation\Rules\Password;

class MembersController extends Controller
{
    public function MembersView()
    {

        $members = User::orderBy('id', 'desc')->paginate(30);
        $searchcat = '';

        $locations     = locations::all();
        $cashierusers = User::whereNot('role_name', 'Admins')->where('location_id', '1')->get();

        return view('/members/index', compact('members', 'searchcat', 'locations', 'cashierusers'));
    }




    public function MemberView($userid = null)
    {

        $memberuserdata = User::find($userid);  

    if (!$memberuserdata) {
        abort(404, 'User not found');
    }



        $from = date('Y-01-01');


        $to = date('Y-m-d');


          $reportData = [];

           $column = str_replace('.', '_', $memberuserdata->username);

            // Handle annual leave parsing separately
            $annualLeave = 0;
            $rows = DB::table('sched')
                ->select($column)
                ->whereBetween('date', [$from, $to])
                ->whereRaw("SUBSTR(`$column`, -12) = 'Annual_Leave'")
                ->get();

            foreach ($rows as $row) {
                $val = $row->$column;
                if (strpos($val, '|') !== false) {
                    $parts = explode('|', $val);
                    $annualLeave += isset($parts[1]) ? $parts[1] : 0;
                }
            }

            // Count for other leave types
            $leaveCounts = [
                'sick_leave' => DB::table('sched')
                    ->whereBetween('date', [$from, $to])
                    ->where($column, 'Sick_Leave')->count(),

                'emergency_leave' => DB::table('sched')
                    ->whereBetween('date', [$from, $to])
                    ->whereRaw("SUBSTR(`$column`, -15) = 'Emergency_Leave'")
                    ->count(),
            ];

            $reportData[] = [

                'annual_leave' => $annualLeave,
                'emergency_leave' => $leaveCounts['emergency_leave'],
                'sick_leave' => $leaveCounts['sick_leave'],
            ];




        return view('/members/view_member', ['userdata' => $memberuserdata,'reportData' => $reportData]);
    }



    public function FilterMembers(Request $request)
    {

        $usernameId  = $request->filteruser;
        $locationId  = $request->filterlocation;

        $searchcat = ''; // if used in view

        $user = Auth::user();
        $query = User::query();

        // Role-based filtering
        if (in_array($user->role_name, ['Agent', 'Leader', 'Senior', 'Supervisor'])) {
            $query->where('location_id', $user->location_id);

            if (in_array($user->role_name, ['Agent', 'Leader'])) {
                $query->where('id', $user->user_id);
            }
        }

        if ($usernameId) {
            $query->where('id', $usernameId);
        }

        if ($locationId) {
            $query->where('location_id', $locationId);
        }

        $members = $query->orderBy('created_at', 'desc')->paginate(30);

        $locations     = locations::all();
        $cashierusers = User::whereNot('role_name', 'Admins')->where('location_id', '1')->get();


        return view('/members/index', compact('members', 'searchcat', 'locations', 'cashierusers', 'usernameId', 'locationId'));
    }






    public function MembersAdd()
    {
        $locations     = locations::all();

        return view('/members/add_member', ['locations' => $locations]);
    }

    public function MembersEdit($memid)
    {

        if ($memid) {


            $memberdata   = User::find($memid);
            $locations     = locations::all();

            return view('/members/edit_member', ['locations' => $locations, 'memberdatas' => $memberdata]);
        } else {
        }
    }

    public function checkUsername($username)
    {
        $exists = User::where('username', $username)->exists();
        return response()->json(['exists' => $exists]);
    }


    public function storeMember(Request $request)

    {
        if ($request->memberid) {

            // $request->validate([
            //     'category' => ['required', 'max:255'],
            //     'discribtion' => 'required',
            //     'catimage' => 'image|mimes:jpeg,png,jpg,gif,svg|max:2048',

            // ]);

            // $categorylink   = category::find($request->pro_family);
            if ($request->status == "Active") {
                $isapp = 1;
                $isban = 0;
            } else {
                $isapp = 0;
                $isban = 1;
            }
            $currectuser = User::find($request->memberid);
            $currectuser->name = $request->full_name;
            $currectuser->ar_name = $request->ar_name;
            $currectuser->emp_num = $request->emp_num;
            $currectuser->phone = $request->phone;
            $currectuser->national_num = $request->national_num;
            $currectuser->location_id = $request->location;
            $currectuser->title = $request->title;
            $currectuser->nick_name = $request->nick_name;
            $currectuser->sys_user = $request->sys_user;
            $currectuser->rank = $request->rank;
            $currectuser->email  = $request->email;
            $currectuser->role_name = $request->title;
            $currectuser->gender = $request->gender;

            $currectuser->isApproved = $isapp;
            $currectuser->isBanned = $isban;
            $currectuser->company = $request->phone;

            $currectuser->language = $request->language;

            if ($request->hasFile('avatar') && $request->file('avatar')->isValid()) {
                $image = $request->file('avatar');

                // Generate a unique name for the image
                $uniqueName = Str::random(20) . '.' . $image->getClientOriginalExtension();

                // Store the image in the 'images' folder of 'public' disk
                $path = $image->storeAs('profile_pic', $uniqueName, 'public');

                $currectuser->profile_pic = $path;
            }


            $currectuser->save();
        } else {


            // Validate input
            $request->validate([
                'password' => 'required|string|min:8|confirmed', // checks if password and password_confirmation match
            ]);



            $user = new User;

            $user->name = $request->full_name;
            $user->email = $request->email;
            $user->username = $request->email;
            $user->role_name = 'Admins';
            $user->role_id = '1';
            $user->isBanned = '0';
            $user->isApproved = '1';
            $user->username = $request->newUsername;
            $user->location_id = '1';
            $user->password = Hash::make($request->password);


            if ($request->hasFile('avatar') && $request->file('avatar')->isValid()) {
                $image = $request->file('avatar');

                // Generate a unique name for the image
                $uniqueName = Str::random(20) . '.' . $image->getClientOriginalExtension();

                // Store the image in the 'images' folder of 'public' disk
                $path = $image->storeAs('profile_pic', $uniqueName, 'public');
                $user->profile_pic = $path;
            }


            $user->save();
        }

        return redirect()->back()->with('success', 'Product added to cart!');
    }

    public function storePassword(Request $request)

    {
        if ($request->passid) {



            // Validate input
            $request->validate([
                'password' => 'required|string|min:8|confirmed', // checks if password and password_confirmation match
            ]);
 
            // Find the user
            $user = User::findOrFail($request->passid);

            // Update the password
            $user->password = Hash::make($request->password);
            $user->save();

            return redirect()->back()->with('success', 'Password updated successfully.');
        }
    }









    public function AdminRoles()
    {

        $rolesnames = roles::all();

        $databaseName = DB::getDatabaseName();
        $tableKey = 'Tables_in_' . $databaseName;

        $tables = array_map(function ($table) use ($tableKey) {
            return $table->$tableKey;
        }, DB::select('SHOW TABLES'));
        return view('/account/roles', ['rolesnames' => $rolesnames, 'dbtables' => $tables]);
    }



    public function AdminROleview($catid = null)
    {


        $roldatas = roles_permisstions::where('role_id', $catid)->get();




        return view('/account/roles_view', ['rolesdata' => $roldatas]);
    }

    public function AdminROleAdd($catid = null)
    {

        $roldatas = roles_permisstions::all();

        return view('/account/roles_add', ['rolesdata' => $roldatas]);
    }



    public function AdminROleDelete($roleid = null)
    {

        if ($roleid) {


            roles::where('id', $roleid)->delete();

            roles_permisstions::where('role_id', $roleid)->delete();


            return redirect()->back()->with('success', 'Product added to cart!');
        }
    }




    public function AccountProfile()
    {
        $userdatas = User::where('id', Auth::user()->id)->get();

        return view('/account/profile', ['userdata' => $userdatas]);
    }

    public function AccountEditProfile()
    {
        $userdatas = User::where('id', Auth::user()->id)->get();



        return view('/account/profile_edit', ['userdata' => $userdatas]);
    }



    public function AdminAccount()
    {

        $useradds  = User::find(Auth::user()->id);


        return view('/account/profile', ['userdatas' => $useradds]);
    }

    public function ChangeLang($lang = null)
    {

        if ($lang) {

            Session()->put('locale', $lang);

            return redirect()->back()->with('success', 'Product added to cart!');
        }
    }


    public function UpdateProfile(Request $request)
    {


        $request->validate([
            'name' => ['required', 'max:255'],
            'mobile' => ['required', 'max:255'],

        ]);

        $currectprofile = User::find(Auth::user()->id);
        $currectprofile->name = $request->name;
        $currectprofile->mobile = $request->mobile;
        $currectprofile->email = $request->email;
        $currectprofile->country = $request->country;
        $currectprofile->languge = $request->languge;
        $currectprofile->gender = $request->gender;
        $currectprofile->birth_date = $request->birth_date;
        $currectprofile->save();


        return redirect()->back()->with('success', 'Product added to cart!');
    }


    public function AdminROleEdit(Request $request)
    {


        // Get checked user IDs (users to set allowView = 1)
        $checkallowView = $request->input('per_read', []);

        // Update checked users (set allowView = 1)
        roles_permisstions::whereIn('id', $checkallowView)->update(['allowView' => 1]);

        // Update unchecked users (set allowView = 0)
        roles_permisstions::whereNotIn('id', $checkallowView)->update(['allowView' => 0]);

        //---------------------------------------------------------------------------------------------

        // Get checked user IDs (users to set allowInsert = 1)
        $checkallowInsert = $request->input('per_write', []);

        // Update checked users (set allowInsert = 1)
        roles_permisstions::whereIn('id', $checkallowInsert)->update(['allowInsert' => 1]);

        // Update unchecked users (set allowInsert = 0)
        roles_permisstions::whereNotIn('id', $checkallowInsert)->update(['allowInsert' => 0]);

        //---------------------------------------------------------------------------------------------

        // Get checked user IDs (users to set allowEdit = 1)
        $checkallowEdit = $request->input('per_edit', []);

        // Update checked users (set allowEdit = 1)
        roles_permisstions::whereIn('id', $checkallowEdit)->update(['allowEdit' => 1]);

        // Update unchecked users (set allowEdit = 0)
        roles_permisstions::whereNotIn('id', $checkallowEdit)->update(['allowEdit' => 0]);

        //---------------------------------------------------------------------------------------------

        // Get checked user IDs (users to set allowDelete = 1)
        $checkallowDelete = $request->input('per_delete', []);

        // Update checked users (set allowDelete = 1)
        roles_permisstions::whereIn('id', $checkallowDelete)->update(['allowDelete' => 1]);

        // Update unchecked users (set allowDelete = 0)
        roles_permisstions::whereNotIn('id', $checkallowDelete)->update(['allowDelete' => 0]);

        //---------------------------------------------------------------------------------------------



        return redirect()->back()->with('success', 'Product added to cart!');
    }


    public function AdminROleStore(Request $request)
    {


        $newprole = new roles;
        $newprole->name = $request->role_name;
        $newprole->allowSignup = 1;
        $newprole->needsApproval = 0;
        $newprole->allowCSVImport = 1;
        $newprole->save();


        $newId = $newprole->id; // Get the inserted ID

        $proimgs = roles_permisstions::where('role_id', 1)->get();


        $databaseName = DB::getDatabaseName();
        $tableKey = 'Tables_in_' . $databaseName;

        $tables = array_map(function ($table) use ($tableKey) {
            return $table->$tableKey;
        }, DB::select('SHOW TABLES'));




        foreach ($tables as $rolenam) {


            roles_permisstions::create([
                'role_id' => $newId,
                'tableName' => $rolenam,
                'allowInsert' => 0,
                'allowView' => 0,
                'allowEdit' => 0,
                'allowDelete' => 0,
                'import' => 0,
                'export' => 0,
            ]);
        }
        return redirect()->back()->with('success', 'Product added to cart!');
    }

    public function LibraryView()
    {

        $libraries = library::orderBy('id', 'desc')->paginate(30);
        $searchcat = '';


        return view('/library', compact('libraries', 'searchcat'));
    }

    public function StoreLibrary(Request $request)

    {

        $user = Auth::user();

        if ($request->memberid) {






            $perform = library::find($request->memberid);
            $perform->title = $request->title;
            $perform->type = $request->type;

            $perform->comment = $request->comment;
            $perform->user_id  = $user->id;
            $perform->location_id  = $user->location_id;




            if ($request->hasFile('upfile') && $request->file('upfile')->isValid()) {
                $image = $request->file('upfile');

                // Generate a unique name for the image
                $uniqueName = Str::random(20) . '.' . $image->getClientOriginalExtension();

                // Store the image in the 'images' folder of 'public' disk
                $path = $image->storeAs('upload', $uniqueName, 'public');

                $perform->profile_pic = $path;
            }

            $perform->save();


        } else {


            $perform = new library;

            $perform->title = $request->title;
            $perform->type = $request->type;

            $perform->comment = $request->comment;
            $perform->user_id  = $user->id;
            $perform->location_id  = $user->location_id;

            if ($request->hasFile('upfile') && $request->file('upfile')->isValid()) {
                $image = $request->file('upfile');

                // Generate a unique name for the image
                $uniqueName = Str::random(20) . '.' . $image->getClientOriginalExtension();

                // Store the image in the 'images' folder of 'public' disk
                $path = $image->storeAs('upload', $uniqueName, 'public');
                $perform->upload = $path;
            }

            $perform->save();
        }

        return redirect()->back()->with('success', 'Product added to cart!');
    }






    public function ChartData3(Request $request)
    {
        $username = $request->query('user_id');
        $locationId = User::where('id', $username)->value('location_id');

        // Achieved mobiles per month
        $mobilesPerMonth = DB::table('mobiles')
            ->selectRaw('MONTH(cashbox_date) as month, COUNT(*) as total')
            ->whereYear('cashbox_date', now()->year)
            ->whereIn('contract_type', ['Control', 'Prepaid', 'postpaid'])
            ->where('user_id', $username)
            ->groupBy('month')
            ->pluck('total', 'month')
            ->toArray();

        // Targets per month
        $targetsPerMonth = DB::table('target_report')
            ->selectRaw('MONTH(date) as month, SUM(quantity) as total')
            ->whereYear('date', now()->year)
            ->where('type', 'Agent_Target')
            ->where('quantity', '>', 0)
            ->where('location_id', $locationId)
            ->whereIn('category', ['Prepaid', 'Control', 'Postpaid'])
            ->groupByRaw('MONTH(date)')
            ->pluck('total', 'month')
            ->toArray();

        $achieved = [];
        $target = [];

        foreach (range(1, 12) as $month) {
            $achieved[] = $mobilesPerMonth[$month] ?? 0;
            $target[] = $targetsPerMonth[$month] ?? 0;
        }

        return response()->json([
            'achieved' => $achieved,
            'target' => $target,
        ]);
    }



    public function ChartData4(Request $request)
    {
        $username = $request->query('user_id');
        $locationId = User::where('id', $username)->value('location_id');

        $mobilesPerMonth = DB::table('adsls')
            ->selectRaw('MONTH(cashbox_date) as month, COUNT(*) as total')
            ->whereYear('cashbox_date', now()->year)
            ->whereIn('contract_type', ['New ADSL'])
            ->where('user_id', $username)
            ->groupBy('month')
            ->orderBy('month')
            ->pluck('total', 'month')
            ->toArray();


        $targetsPerMonth = DB::table('target_report')
            ->selectRaw('MONTH(date) as month, SUM(quantity) as total')
            ->whereYear('date', now()->year)
            ->where('type', 'Agent_Target')
            ->where('quantity', '>', 0)
            ->where('location_id', $locationId)
            ->where('category', 'ADSL')           
            ->groupByRaw('MONTH(date)')
            ->pluck('total', 'month')
            ->toArray();

        $achieved = [];
        $target = [];

        foreach (range(1, 12) as $month) {
            $achieved[] = $mobilesPerMonth[$month] ?? 0;
            $target[] = $targetsPerMonth[$month] ?? 0;
        }

        return response()->json([
            'achieved' => $achieved,
            'target' => $target,
        ]);


    }






}
