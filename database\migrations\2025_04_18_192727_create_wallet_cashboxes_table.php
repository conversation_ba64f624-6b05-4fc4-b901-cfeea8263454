<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('wallet_cashboxes', function (Blueprint $table) {
            $table->id();

            $table->date('cashbox_date')->nullable();

            $table->string('tran_id')->nullable();
            $table->string('tran_type')->nullable();
            $table->string('number')->nullable();
            $table->string('reference')->nullable();
            $table->float('amount', 1, 2)->nullable();
            
            $table->unsignedBigInteger('location_id')->nullable();
            $table->foreign('location_id')->references('id')->on('locations')->onDelete('cascade');
            $table->unsignedBigInteger('user_id')->nullable();
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->string('status')->nullable()->default('Pending');

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('wallet_cashboxes');
    }
};
