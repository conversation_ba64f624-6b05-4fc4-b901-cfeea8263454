<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\mobile_packages;
use App\Models\mobile_products;
use App\Models\adsl;
use App\Models\User;
use App\Models\locations;

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;

class ADSLController extends Controller
{
    public function ADSLView()
    {

        $allpackages = mobile_packages::where('package_type', 'ADSL')->whereNull('num')->get();

        // Get users and locations for filter dropdowns
        $users = User::select('id', 'nick_name', 'username', 'name')->get();
        $locations = locations::select('id', 'name_en', 'name_ar')->get();

        if (in_array(Auth::user()->role_name, ['Agent', 'Leader', 'Senior', 'Supervisor'])) {

            $query = adsl::where('location_id', Auth::user()->location_id);

            if (in_array(Auth::user()->role_name, ['Agent', 'Leader'])) {

                $query->where('user_id', Auth::user()->id);
            }

            $allcontracts = $query->orderBy('cashbox_date', 'desc')->paginate(30);
        } else {
            $allcontracts = adsl::orderBy('cashbox_date', 'desc')->paginate(30);
        }







        return view('adsl/adslview', [
            'contratcs' => $allcontracts,
            'searchcat' => '',
            'packages' => $allpackages,
            'users' => $users,
            'locations' => $locations
        ]);
    }

    public function SearchADSL(Request $request)
    {
        $search = $request->searchcategory;
        $allpackages = mobile_packages::where('package_type', 'ADSL')->whereNull('num')->get();

        $query = adsl::with(['packages', 'users', 'location'])
            ->where(function ($q) use ($search) {
                $q->where('number', 'like', '%' . $search . '%')
                  ->orWhere('contract_type', 'like', '%' . $search . '%')
                  ->orWhere('comment', 'like', '%' . $search . '%');
            })
            ->orWhereHas('packages', function ($q) use ($search) {
                $q->where('package_name', 'like', '%' . $search . '%');
            })
            ->orWhereHas('users', function ($q) use ($search) {
                $q->where('nick_name', 'like', '%' . $search . '%')
                  ->orWhere('username', 'like', '%' . $search . '%')
                  ->orWhere('name', 'like', '%' . $search . '%');
            })
            ->orWhereHas('location', function ($q) use ($search) {
                $q->where('name_en', 'like', '%' . $search . '%')
                  ->orWhere('name_ar', 'like', '%' . $search . '%');
            });

        // Apply role-based filtering
        if (in_array(Auth::user()->role_name, ['Agent', 'Leader', 'Senior', 'Supervisor'])) {
            $query->where('location_id', Auth::user()->location_id);

            if (in_array(Auth::user()->role_name, ['Agent', 'Leader'])) {
                $query->where('user_id', Auth::user()->id);
            }
        }

        $allcontracts = $query->orderBy('cashbox_date', 'desc')->paginate(10);

        $users = User::select('id', 'nick_name', 'username', 'name')->get();
        $locations = locations::select('id', 'name_en', 'name_ar')->get();

        return view('adsl/adslview', [
            'contratcs' => $allcontracts,
            'searchcat' => $search,
            'packages' => $allpackages,
            'users' => $users,
            'locations' => $locations
        ]);
    }

    public function FilterADSL(Request $request)
    {
        $datefrom = $request->fildatefrom;
        $dateto = $request->fildateto;
        $status = $request->filstatus;
        $packageId = $request->filpackage;
        $userId = $request->filuser;
        $locationId = $request->fillocation;

        $allpackages = mobile_packages::where('package_type', 'ADSL')->whereNull('num')->get();
        $users = User::select('id', 'nick_name', 'username', 'name')->get();
        $locations = locations::select('id', 'name_en', 'name_ar')->get();

        $query = adsl::with(['packages', 'users', 'location']);

        // Apply role-based filtering
        if (in_array(Auth::user()->role_name, ['Agent', 'Leader', 'Senior', 'Supervisor'])) {
            $query->where('location_id', Auth::user()->location_id);

            if (in_array(Auth::user()->role_name, ['Agent', 'Leader'])) {
                $query->where('user_id', Auth::user()->id);
            }
        }

        // Apply filters
        if ($datefrom && $dateto) {
            $query->whereBetween('cashbox_date', [$datefrom, $dateto]);
        }

        if ($status && $status !== 'Status') {
            $query->where('status', $status);
        }

        if ($packageId) {
            $query->where('package_code', $packageId);
        }

        if ($userId) {
            $query->where('user_id', $userId);
        }

        if ($locationId) {
            $query->where('location_id', $locationId);
        }

        $allcontracts = $query->orderBy('cashbox_date', 'desc')->paginate(30);

        return view('adsl/adslview', [
            'contratcs' => $allcontracts,
            'searchcat' => '',
            'packages' => $allpackages,
            'users' => $users,
            'locations' => $locations
        ]);
    }

    public function StoreADSL(Request $request)
    {

        $user = Auth::user();
        $username = $user->email;

        if ($request->catid) {

            // $request->validate([
            //     'category' => ['required', 'max:255'],
            //     'discribtion' => 'required',
            //     'catimage' => 'image|mimes:jpeg,png,jpg,gif,svg|max:2048',

            // ]);

            // $categorylink   = category::find($request->pro_family);



            // $currectcategory = product::find($request->catid);
            // $currectcategory->category_id = $categorylink->id;
            // $currectcategory->sub_category = $categorylink->sub_category;
            // $currectcategory->family = $categorylink->family;
            // $currectcategory->product_name = $request->pro_name;
            // $currectcategory->product_code = $request->pro_code;
            // $currectcategory->product_sku = $request->product_sku;
            // $currectcategory->product_type = $request->pro_type;
            // $currectcategory->brand = $request->pro_brand;
            // $currectcategory->size = $request->pro_size;
            // $currectcategory->color = $request->pro_color;
            // $currectcategory->ord_qty = $request->pro_max_qty;
            // $currectcategory->stock_qty = $request->pro_quantity;
            // $currectcategory->price = $request->pro_price;
            // $currectcategory->old_price = $request->pro_olprice;
            // $currectcategory->offer = $request->pro_offer;
            // $currectcategory->username = $username;
            // $currectcategory->status  = $request->cusstatus;
            // $currectcategory->discribtion = $request->description;


            // $currectcategory->save();

        } else {

            $request->validate([
                'mob_sku' => ['required', 'max:255'],
                'mob_number' => 'required',

            ]);



            // $validatepro = mobile_products::where('product_code', $request->pro_code)->where('size', $request->pro_size)->where('color', $request->pro_color)->get();
            $user = Auth::user();
  
    

                $mobilepackge = mobile_packages::find($request->mob_package);


                $newmobile = new adsl;
                $newmobile->product_sku = $request->mob_sku;
                $newmobile->number =$request->mob_number;
                $newmobile->comment = $request->description;
                $newmobile->addons = $request->mob_addon;
                $newmobile->package_code = $request->mob_package;

                $newmobile->contract_type = $mobilepackge->package_type;

                
                $newmobile->user_id = $user->id;
                $newmobile->location_id =$user->location;

                $newmobile->save();
                $newId = $newmobile->id; // Get the inserted ID




            }
        

        return redirect('admin/products/addproduct');

        return redirect('admin/products/editproduct/' . $request->catid);


    }


    public function EditADSL($catid = null)
    {

        $currectcategory = adsl::findorfail($catid);






        
        return view('admin/categories/editcategory', ['categorydata' => $currectcategory]);
    }
}
