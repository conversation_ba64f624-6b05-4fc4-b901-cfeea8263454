<?php

namespace App\Imports;

use App\Models\User;
use App\Models\warehouse;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithStartRow;

class StockImport implements ToModel, WithStartRow
{

    /**
     * @param array $row
     *
     * @return \Illuminate\Database\Eloquent\Model|null
     */


    protected $type;

    // Add constructor to receive type
    public function __construct($type)
    {
        $this->type = $type;
    }




    public function model(array $row)
    {
        if ($this->type == 'Package Query') {
            $productSku = $row[1];
            $Skucode = $row[2];
            $productname = $row[3];
            $productstatus = $row[4];
            $warehouse = $row[5];
            $warehouseou = $row[6];
        }

        if ($this->type == 'SIM Card Query') {
            $productSku = $row[1];
            $Skucode = $row[7];
            $productname = $row[8];
            $productstatus = $row[12];
            $warehouse = $row[10];
            $warehouseou = $row[11];
        }



        // Skip if product_sku is empty
        if (empty($productSku)) {
            return null;
        }

        // Skip if product_sku already exists in the database
        if (Warehouse::where('product_sku', $productSku)->exists()) {
            return null;
        }

        return new Warehouse([
            'product_sku'  => $productSku,
            'sku_code' => $Skucode,
            'product_name' => $productname,
            'location_id' => 1,
            'warehouse' => $warehouse,
            'product_type' => $this->type,
            'Warehouse_ou' => $warehouseou,
            'status' => $productstatus,
        ]);
    }




    public function startRow(): int
    {
        return 2;
    }
}
