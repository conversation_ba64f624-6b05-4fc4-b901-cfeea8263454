<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\mobile_packages;
use App\Models\mobile_products;
use App\Models\landline;

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;

class LandlineController extends Controller
{
    public function LandlineView()
    {
        $allpackages = mobile_packages::where('package_type', 'Fixed')->whereNull('num')->get();


        if (in_array(Auth::user()->role_name, ['Agent', 'Leader', 'Senior', 'Supervisor'])) {

            $query = landline::where('location_id', Auth::user()->location_id);
        
            if (in_array(Auth::user()->role_name, ['Agent', 'Leader'])) {

                $query->where('user_id', Auth::user()->id);
            }
        
            $allcontracts = $query->orderBy('cashbox_date', 'desc')->paginate(30);
        } else {
            $allcontracts = landline::orderBy('cashbox_date', 'desc')->paginate(30);
        }




        return view('landline/landlineview', ['contratcs' => $allcontracts, 'searchcat' => '','packages' => $allpackages]);
    }

    public function StoreLandline(Request $request)
    {

        $user = Auth::user();
        $username = $user->email;
        $todatdate = date('Y-m-d');
        if ($request->cusappid) {

            $request->validate([
                'up_num' => ['required', 'max:255'],
                'up_type' => 'required',

            ]);

            $mobilepackgename = mobile_packages::find($request->up_package);

            $currectlandline = landline::find($request->cusappid);

            $currectlandline->number = $request->up_num;
            $currectlandline->contract_type = $request->up_type;
            $currectlandline->package_code  = $request->up_package;

            $currectlandline->package_name = $mobilepackgename->package_name;


            $currectlandline->comment = $request->up_comment;

            $currectlandline->save();

        } else {

            $request->validate([
                'new_num' => ['required', 'max:255'],
                'new_type' => 'required',

            ]);


            $user = Auth::user();
  

                $mobilepackgename = mobile_packages::find($request->new_package);


                $newlandline = new landline;
                $newlandline->cashbox_date = $todatdate;
                $newlandline->number =$request->new_num;
                $newlandline->contract_type = $request->new_type;
                $newlandline->package_code = $request->new_package;
                $newlandline->package_name = $mobilepackgename->package_name;
                $newlandline->comment = $request->nwcomment;
                $newlandline->location_id  = $user->location_id;
                $newlandline->user_id  = $user->id;

                $newlandline->save();




            }
        

            return redirect()->back()->with('success', 'Your action was successful!');



    }


    public function EditLandline($catid = null)
    {

        $currectcategory = landline::findorfail($catid);

        return view('admin/categories/editcategory', ['categorydata' => $currectcategory]);
    }
}
