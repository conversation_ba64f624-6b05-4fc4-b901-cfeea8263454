<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Models\app_details;
use Illuminate\Support\Facades\View;

class AppSetting
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {

        $adddetails = app_details::all();

        // Share with all views
        View::share([
            'appdata' => $adddetails,
        ]);

        return $next($request);
    }
}
