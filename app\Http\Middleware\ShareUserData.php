<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\View;
use App\Models\alerts;
use App\Models\User;
use App\Models\logs;
use Illuminate\Support\Facades\Session;
use App\Http\Controllers\Auth\AuthenticatedSessionController;




class ShareUserData
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $user = Auth::user();



        // Share the authenticated user (can be null)
        View::share('authUser', $user);

        if ($user) {
            $now_time = time();
            $session_timeout = 1780;

            $todaydate11 = date('Y-m-d');

            $lastActivity = User::where('id', $user->id)->value('session_start');

            if ($lastActivity > 0) {

                $session_duration = $now_time - $lastActivity;

                if ($session_duration > $session_timeout) {

                    User::where('id', $user->id)->update(['session_start' => NULL]);

                    $controller = new AuthenticatedSessionController();
                    return $controller->destroy($request);
                } else {
                    // ✅ Session active: update session start
                    User::where('id', $user->id)->update(['session_start' => $now_time]);
                }
            } else {

                User::where('id', $user->id)->update(['session_start' => $now_time]);
            }
            $queryalerts = alerts::where('user_id', $user->id)->orderBy('created_at', 'desc')->limit(5)->get();
            $alertscount = alerts::where('user_id', $user->id)->count();
            $querylogs = logs::where('user_id', $user->id)->orderBy('created_at', 'desc')->limit(5)->get();
            $querylate = logs::where('user_id', $user->id)->where('comment', 'Late Attendance')->orderBy('date', 'desc')->limit(5)->get();

            // $user_shift = DB::table('sched')
            // ->where('date', $todaydate)
            // ->value($schedtable);
            $querysignin = logs::where('user_id', $user->id)->where('date', $todaydate11)->value('signin');
            $querysignout = logs::where('user_id', $user->id)->where('date', $todaydate11)->value('signout');

            View::share('signin', $querysignin);
            View::share('signout', $querysignout);
            View::share('alerts', $queryalerts);
            View::share('alertscount', $alertscount);
            View::share('logs', $querylogs);
            View::share('querylate', $querylate);
        } else {
            View::share('alerts', collect());
            View::share('logs', collect());
        }
        return $next($request);
    }
}
