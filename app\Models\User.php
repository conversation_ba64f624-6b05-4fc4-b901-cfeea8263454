<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }




    public function roles() // ✅ Use singular "role" (a user has ONE role)
    {
        return $this->belongsTo(roles::class, 'role_id', 'id'); // ✅ Corrected model name


    }

    public function permissions()
    {
        return $this->roles ? $this->roles->permissions : collect();
    }

    public function hasPermission($table, $action)
    {
        return $this->permissions()
            ->where('tableName', $table)
            ->where($action, true)
            ->isNotEmpty();
    }


    public function location()
    {
        return $this->belongsTo(locations::class, 'location_id', 'id');
    }


public function recivedmessages()
{
    return $this->hasMany(chatlogs::class, 'receiver_id', 'id'); // Adjust foreign key if different

        // return $this->hasMany(chatlogs::class); // adjust the related model and foreign key if needed

}


}
