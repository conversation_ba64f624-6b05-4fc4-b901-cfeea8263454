<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('landlines', function (Blueprint $table) {
            $table->id();

            $table->date('cashbox_date')->nullable();

            $table->string('number')->nullable();
            $table->string('contract_type')->nullable();
            $table->string('comment',800)->nullable();

            $table->unsignedBigInteger('package_code')->nullable();
            $table->foreign('package_code')->references('id')->on('mobile_packages')->onDelete('cascade');
            $table->string('package_name')->nullable();

            $table->unsignedBigInteger('location_id')->nullable();
            $table->foreign('location_id')->references('id')->on('locations')->onDelete('cascade');

            $table->unsignedBigInteger('user_id')->nullable();
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');

            $table->string('status')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('landlines');
    }
};
