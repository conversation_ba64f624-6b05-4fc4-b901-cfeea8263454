<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class roles_permisstions extends Model
{
    protected $fillable = [
        'role_id', // ✅ Added for mass assignment
        'tableName', // ✅ Add other columns if needed
        'allowInsert', // ✅ Add other columns if needed
        'allowView', // ✅ Add other columns if needed
        'allowEdit', // ✅ Add other columns if needed
        'allowDelete', // ✅ Add other columns if needed
        'import', // ✅ Add other columns if needed
        'export', // ✅ Add other columns if needed
    ];


    public function roles()
    {
        return $this->belongsTo(roles::class, 'role_id');
    }


}
