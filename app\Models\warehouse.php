<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class warehouse extends Model
{
    protected $fillable = [
        'product_sku',
        'product_name',
        'product_type',
        'sku_code',
        'price',
        'location_id',
        'warehouse',
        'Warehouse_ou',
    ];

    public function location()
    {
        return $this->belongsTo(locations::class, 'location_id', 'id');
    }


}
