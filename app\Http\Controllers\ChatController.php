<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use App\Models\chatlogs;
use App\Models\User;
use Illuminate\Support\Facades\Storage;

class ChatController extends Controller
{
    public function ChatView()
    {
        $user = Auth::user();
        $username = $user->id;
        $user_location = $user->location_id;


        $users = User::where('location_id', $user_location)->get();


        return view('tools/chat', ['usersdata' => $users]);
    }



    public function getUsers(Request $request)
    {
        $userId = $request->query('userid');
        $user = Auth::user();
        $username = $user->id;
        $user_location = $user->location_id;

        $usersQuery = User::where('location_id', $user_location)->whereNot('id', $username);

        if (!empty($userId)) {

            $usersQuery->where(function ($query) use ($userId) {
                $query->where('id', 'like', "%{$userId}%")
                    ->orWhere('name', 'like', "%{$userId}%")
                    ->orWhere('email', 'like', "%{$userId}%");
            });
        }

        $users = $usersQuery->get();


        // Attach pending message count per user
        foreach ($users as $userItem) {
            $userItem->pending_count = chatlogs::where('sender_id', $userItem->id)
                ->where('receiver_id', $username)
                ->where('status', 'pending') // filter pending messages
                ->count();
        }


        return view('tools.chat_users', ['username' => $username, 'usersdata' => $users]);
    }



    public function getChat(Request $request)
    {
        $userId = $request->query('userid');

        $user = Auth::user();
        $username = $user->id;
        // Example: Fetch messages or chat details
        // $chatData = DB::table('chatlogs')
        //     ->where('request_number', $reqnum)
        //     ->orderBy('created_at', 'desc')
        //     ->get();

        // $chatData = DB::table('chatlogs')
        // ->orderBy('created_at', 'desc')
        // ->get();

        // $chatData = chatlogs::orderBy('created_at', 'desc')->get();



        //         $usersQuery = chatlogs::where('sender_id', $username)->where('receiver_id', $userId);
        //   $chatData = $usersQuery->get();



        // if (!empty($userId)) {

        //     $usersQuery->where(function ($query) use ($userId) {
        //         $query->where('id', 'like', "%{$userId}%")
        //         ->orWhere('name', 'like', "%{$userId}%")
        //         ->orWhere('email', 'like', "%{$userId}%");
        //     });

        // }

        $chatData = chatlogs::where(function ($query) use ($username, $userId) {
            $query->where('sender_id', $username)
                ->where('receiver_id', $userId);
        })
            ->orWhere(function ($query) use ($username, $userId) {
                $query->where('sender_id', $userId)
                    ->where('receiver_id', $username);
            })
            ->orderBy('created_at', 'desc') // optional: sort by time
            ->get();





        chatlogs::where('receiver_id', $username)->where('sender_id', $userId)->update(['status' => 'Viewed']);


        // If returning HTML (like a partial view)
        return view('tools.chating_body', ['chatData' => $chatData, 'username' => $username]);

        // Or, if using JSON:
        // return response()->json($chatData);
    }


    public function addChat(Request $request)
    {
        // $request->validate([
        //     'message' => 'nullable|string|max:1000',
        //     'req_num' => 'required|string',
        //     'file' => 'nullable|image|mimes:jpg,jpeg,png,gif|max:2048'
        // ]);


        $user = Auth::user();
        $username = $user->id;

        $filename = null;

        if ($request->hasFile('file')) {
            $filename = $request->file('file')->store('chat_uploads', 'public');
        }

        // Save to database (assuming a Message model exists)
        \App\Models\chatlogs::create([
            'sender_id' => $username,
            'receiver_id' => $request->req_num,
            'chat_message' => $request->message,
            'upload' => $filename,
            'status' => 'Pending',
        ]);

        return response()->json(['status' => 'success']);
    }


    public function checkNewMessages()
    {
        $user = Auth::user();
        $username = $user->id;

        $messages = chatlogs::where('receiver_id', $username)
            ->where('status', 'Pending')
            ->latest()
            ->get();

        return response()->json(['messages' => $messages]);
    }

    public function checkCountMessages(Request $request)

    {
        $username = $request->query('user_id');

        // $user = Auth::user();
        // $username = $user->id;
        //    $messages = chatlogs::where('receiver_id', $username)
        //         ->where('status', 'Pending')
        //         ->orderBy('created_at')
        //         ->get(['id', 'sender_id', 'chat_message', 'created_at']);

        //     $count = $messages->count();

        $now_time = time();
        $session_timeout = 1800;

        $lastActivity = User::where('id', $username)->value('session_start');


        $session_duration = $now_time - $lastActivity;

        if ($lastActivity > 0) {

            if ($session_duration > $session_timeout) {

                User::where('id', $username)->update(['session_start' => Null]);

                return response()->json(['status' => 'Failed', 'message' => 'Session expired']);
            }
        }
        $messages = chatlogs::where('chatlogs.receiver_id', $username)
            ->where('chatlogs.status', 'Pending')
            ->join('users', 'users.id', '=', 'chatlogs.sender_id')
            ->orderBy('chatlogs.created_at')
            ->get([
                'chatlogs.id',
                'chatlogs.sender_id',
                'users.nick_name as sender_name',
                'chatlogs.chat_message',
                'chatlogs.created_at'
            ]);

        $count = $messages->count();

        return response()->json([
            'messages' => $messages,
            'total_pending' => $count
        ]);
    }

    public function Test()
    {
        return response()->json(['message' => 'API route is working']);
    }

    public function checkNewMessages1()
    {
        $user = Auth::user();
        $username = $user->id;

        $data = DB::table('users')->where('users.location_id', '=', '1')
            ->leftJoin('chatlogs', function ($join) use ($username) {
                $join->on('users.id', '=', 'chatlogs.sender_id')
                    ->where('chatlogs.receiver_id', '=', $username)
                    ->where('chatlogs.status', '=', 'Pending');
            })
            ->select('users.id', 'users.name', DB::raw('COUNT(chatlogs.id) as pending_message_count'))
            ->groupBy('users.id', 'users.name')
            ->get();


        return response()->json($data);
    }
}
