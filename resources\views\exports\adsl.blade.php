<table>
    <thead>
        <tr>
            <th>ID</th>
            <th>ADSL Number</th>
            <th>Package</th>
            <th>Status</th>
            <th>Username</th>
            <th>Cashbox Date</th>
            <th>Created Date</th>
            <th>Location</th>
        </tr>
    </thead>
    <tbody>
        @foreach($adsls as $adsl)
            <tr>
                <td>{{ $adsl->id }}</td>
                <td>{{ $adsl->number }}</td>
                <td>{{ $adsl->packages->package_name ?? $adsl->contract_type }}</td>
                <td>{{ $adsl->status }}</td>
                <td>{{ $adsl->users->nick_name ?? $adsl->users->username ?? 'N/A' }}</td>
                <td>{{ $adsl->cashbox_date ?? 'N/A' }}</td>
                <td>{{ $adsl->created_at }}</td>
                <td>{{ $adsl->location->name_en ?? $adsl->location_id }}</td>
            </tr>
        @endforeach
    </tbody>
</table>