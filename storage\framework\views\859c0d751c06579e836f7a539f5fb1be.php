<table>
    <thead>
        <tr>
            <th>ID</th>
            <th>ADSL Number</th>
            <th>Package</th>
            <th>Status</th>
            <th>Date</th>
            <th>Location</th>
        </tr>
    </thead>
    <tbody>
        <?php $__currentLoopData = $adsls; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $adsl): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <tr>
                <td><?php echo e($adsl->id); ?></td>
                <td><?php echo e($adsl->number); ?></td>
                <td><?php echo e($adsl->packages->package_name ?? $adsl->contract_type); ?></td>
                <td><?php echo e($adsl->status); ?></td>
                <td><?php echo e($adsl->created_at); ?></td>
                <td><?php echo e($adsl->location->name_en ?? $adsl->location_id); ?></td>
            </tr>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </tbody>
</table><?php /**PATH D:\xampp\htdocs\wetool\resources\views/exports/adsl.blade.php ENDPATH**/ ?>