<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use App\Models\challanges;



class DashboardController extends Controller
{



    public function DashboardRedirect()
    {
        $user = Auth::user();

        if (in_array(Auth::user()->role_name, ['Ad<PERSON>', 'Senior', 'Supervisor'])) {
            return $this->DashboardView(); // Admin dashboard
        } else {
            return $this->DashboardUserView(); // Regular user dashboard
        }
    }

    public function getChartData()
    {
        $currentMonth = Carbon::now()->month;
        $currentYear = Carbon::now()->year;

        // 1️⃣ Get Sales Data from Database
        $salesData = DB::table('mobiles')
            ->select(DB::raw('DAY(created_at) as day'), DB::raw('count(product_sku) as Sales'))
            ->whereMonth('created_at', $currentMonth)
            ->whereYear('created_at', $currentYear)
            ->groupBy(DB::raw('DAY(created_at)'))
            ->pluck('Sales', 'day') // Returns associative array [ day => Sales ]
            ->toArray();

        // 2️⃣ Generate All Days of the Current Month
        $daysInMonth = Carbon::now()->daysInMonth;
        $chartData = [];

        for ($day = 1; $day <= $daysInMonth; $day++) {
            $chartData[] = [
                "day" => (string) $day, // Convert to string to match your format
                "Sales" => $salesData[$day] ?? 0, // Use sales data if available, else 0
                "icon" => "https://www.amcharts.com/wp-content/uploads/flags/united-states.svg",
                "columnSettings" => [
                    "fill" => "am5.color(KTUtil.getCssVariableValue('--bs-primary'))"
                ]
            ];
        }

        return response()->json($chartData);
    }

    public function DashboardView()
    {
        $month = request('month', \Carbon\Carbon::now()->format('Y-m')); // Example full month string request('month', \Carbon\Carbon::now()->format('Y-m'));
        $date = Carbon::createFromFormat('Y-m', $month);
        $user = Auth::user();

        $datos = $user->id;
        $location = $user->location_id;

        $startOfMonth = $date->copy()->startOfMonth()->toDateString();
        $endOfMonth = $date->copy()->endOfMonth()->toDateString();

        $days = collect(range(1, $date->daysInMonth))->map(fn($d) => $date->copy()->day($d)->toDateString());
        // Helper to build daily counts
        $dailyCounts = function ($table, $dateField, $filterField, $filterValues) use ($startOfMonth, $endOfMonth) {
            return DB::table($table)
                ->selectRaw("DATE($dateField) as date, COUNT(*) as total")
                ->when($filterField && $filterValues, function ($q) use ($filterField, $filterValues) {
                    $q->whereIn($filterField, $filterValues);
                })
                ->whereBetween($dateField, [$startOfMonth, $endOfMonth])
                ->groupBy(DB::raw("DATE($dateField)"))
                ->pluck('total', 'date'); // ['2025-05-01' => 4, ...]
        };

        $mobileCounts = $dailyCounts('mobiles', 'cashbox_date', 'contract_type', ['prepaid', 'postpaid', 'control']);
        $adslCounts   = $dailyCounts('adsls', 'cashbox_date', 'contract_type', ['New ADSL']);
        $walletCounts = $dailyCounts('mobiles', 'cashbox_date', 'contract_type', ['Wallet']);
        $landlineCounts = $dailyCounts('landlines', 'cashbox_date', 'contract_type', ['New Landline']);

        $mobileData = $days->map(fn($d) => $mobileCounts[$d] ?? 0);
        $walletData = $days->map(fn($d) => $walletCounts[$d] ?? 0);
        $adslData = $days->map(fn($d) => $adslCounts[$d] ?? 0);
        $landlineData = $days->map(fn($d) => $landlineCounts[$d] ?? 0);



        // $topUsers = DB::table('users')
        //     ->where('users.location_id', '=', $user->location_id)->whereIn('users.role_name', ['Agent', 'Leader']) // ✅ restrict to same location
        //     ->leftJoin('mobiles', function ($join) use ($startOfMonth, $endOfMonth, $user) {
        //         $join->on('users.id', '=', 'mobiles.user_id')
        //              ->whereIn('mobiles.contract_type', ['prepaid', 'postpaid', 'control'])
        //              ->whereBetween('mobiles.cashbox_date', [$startOfMonth, $endOfMonth])
        //              ->where('mobiles.location_id', '=', $user->location_id);
        //     })
        //     ->leftJoin('adsls', function ($join) use ($startOfMonth, $endOfMonth, $user) {
        //         $join->on('users.id', '=', 'adsls.user_id')
        //              ->whereBetween('adsls.cashbox_date', [$startOfMonth, $endOfMonth])
        //              ->where('adsls.location_id', '=', $user->location_id);
        //     })
        //     ->leftJoin('target_report as t1', function ($join) use ($startOfMonth, $endOfMonth, $user) {
        //         $join->on('users.location_id', '=', 't1.location_id')
        //              ->whereIn('t1.category', ['Control', 'Postpaid', 'Prepaid'])
        //              ->where('t1.type', '=', 'Agent_Target')
        //              ->whereBetween('t1.date', [$startOfMonth, $endOfMonth])
        //              ->where('t1.location_id', '=', $user->location_id);
        //     })
        //     ->leftJoin('target_report as t2', function ($join) use ($startOfMonth, $endOfMonth, $user) {
        //         $join->on('users.location_id', '=', 't2.location_id')
        //              ->where('t2.category', '=', 'ADSL')
        //              ->where('t2.type', '=', 'Agent_Target')
        //              ->whereBetween('t2.date', [$startOfMonth, $endOfMonth]);
        //     })
        //     ->select(
        //         'users.id',
        //         'users.name',
        //         'users.profile_pic',
        //         DB::raw('COUNT(DISTINCT mobiles.id) as mobile_sales'),
        //         DB::raw('COUNT(DISTINCT adsls.id) as adsl_sales'),
        //         DB::raw('IFNULL(t1.quantity, 0) as mobile_target'),
        //         DB::raw('IFNULL(t2.quantity, 0) as adsl_target'),
        //         DB::raw('
        //             ROUND(
        //                 (
        //                     (COUNT(DISTINCT mobiles.id) + COUNT(DISTINCT adsls.id)) /
        //                     NULLIF((IFNULL(t1.quantity, 0) + IFNULL(t2.quantity, 0)), 0)
        //                 ) * 100, 2
        //             ) as achievement_percent
        //         ')
        //     )
        //     ->groupBy(
        //         'users.id',
        //         'users.name',
        //         'users.profile_pic',
        //         't1.quantity',
        //         't2.quantity'
        //     )
        //     ->orderByDesc('achievement_percent')
        //     ->limit(20)
        //     ->get();














        // Sum targets for the location (no user_id)
        $targets = DB::table('target_report')
            ->select(
                DB::raw('SUM(CASE WHEN category IN ("Control", "Postpaid", "Prepaid") THEN quantity ELSE 0 END) as mobile_target'),
                DB::raw('SUM(CASE WHEN category = "ADSL" THEN quantity ELSE 0 END) as adsl_target')
            )
            ->where('location_id', $location)
            ->where('type', 'Agent_Target')
            ->whereBetween('date', [$startOfMonth, $endOfMonth])
            ->first();

        // Subquery: mobile sales per user
        $mobileSalesSub = DB::table('mobiles')
            ->select('user_id', DB::raw('COUNT(id) as mobile_sales'))
            ->whereIn('contract_type', ['prepaid', 'postpaid', 'control'])
            ->whereBetween('cashbox_date', [$startOfMonth, $endOfMonth])
            ->where('location_id', $location)
            ->groupBy('user_id');

        // Subquery: adsl sales per user
        $adslSalesSub = DB::table('adsls')
            ->select('user_id', DB::raw('COUNT(id) as adsl_sales'))
            ->whereBetween('cashbox_date', [$startOfMonth, $endOfMonth])
            ->where('location_id', $location)
            ->groupBy('user_id');


        $totalTarget = ($targets->mobile_target ?? 0) + ($targets->adsl_target ?? 0);

        // Get users in location with sales joined
        $topUsers = DB::table('users')
            ->where('location_id', $location)->whereIn('users.role_name', ['Agent', 'Leader'])
            ->leftJoinSub($mobileSalesSub, 'mobile_sales', function ($join) {
                $join->on('users.id', '=', 'mobile_sales.user_id');
            })
            ->leftJoinSub($adslSalesSub, 'adsl_sales', function ($join) {
                $join->on('users.id', '=', 'adsl_sales.user_id');
            })
            ->select(
                'users.id',
                'users.name',
                'users.profile_pic',
                DB::raw('IFNULL(mobile_sales.mobile_sales, 0) as mobile_sales'),
                DB::raw('IFNULL(adsl_sales.adsl_sales, 0) as adsl_sales'),
                DB::raw("
            ROUND(
                (
                    (IFNULL(mobile_sales.mobile_sales, 0) + IFNULL(adsl_sales.adsl_sales, 0)) /
                    NULLIF($totalTarget, 0)
                ) * 100, 2
            ) as achievement_percent
        ")
            )
            ->orderByDesc('achievement_percent')
            ->limit(20)
            ->get();


        $adslsPerMonth = DB::table('adsls')
            ->selectRaw('MONTH(created_at) as month, COUNT(*) as total')
            ->whereYear('created_at', now()->year)
            ->groupBy('month')
            ->orderBy('month')
            ->pluck('total', 'month')
            ->toArray();

        // Fill missing months
        $monthlyCounts = collect(range(1, 12))->mapWithKeys(function ($m) use ($adslsPerMonth) {
            return [$m => $adslsPerMonth[$m] ?? 0];
        });


        // $location = $request->input('location', $location);
        $startDate = Carbon::now()->startOfMonth()->toDateString();
        $endDate = Carbon::now()->endOfMonth()->toDateString();

        // Helper query function
        $targetSum = fn($conditions) =>
        DB::table('target_report')->selectRaw('SUM(quantity) as total')
            ->whereBetween('date', [$startDate, $endDate])
            ->where('type', 'Location_Target')
            ->where('quantity', '>', 0)

            ->where('location', $location)
            ->where($conditions)
            ->first()->total ?? 0;


        $countSum = fn($table, $conditions) =>
        DB::table($table)->selectRaw('COUNT(*) as total')
            ->whereBetween('cashbox_date', [$startDate, $endDate])
            ->where('location_id', $location)
            ->where($conditions)
            ->first()->total ?? 0;

        $targets = [
            'prepaid' => $targetSum(fn($q) => $q->whereIn('category', ['Prepaid', 'Control', 'Postpaid'])),
            'landline' => $targetSum(fn($q) => $q->where('category', 'Fixed')),
            'adsl' => $targetSum(fn($q) => $q->where('category', 'ADSL')),
            'wallet' => $targetSum(fn($q) => $q->where('category', 'Wallet')),
        ];



        $actuals = [
            'prepaid' => $countSum(
                'mobiles',
                fn($q) => $q->whereIn('contract_type', ['Control', 'Prepaid', 'Postpaid'])
            ),
            'landline' => $countSum('landlines', fn($q) => $q->where('contract_type', 'New Landline')),
            'adsl' => $countSum('adsls', fn($q) => $q->where('contract_type', 'New ADSL')),
            'wallet' => $countSum('mobiles', fn($q) => $q->where('contract_type', 'Wallet')),
        ];

        $percentages = [];
        foreach ($targets as $key => $val) {


            $required = max(1, round($val));

            $achieved = $actuals[$key];

            $percentages[$key] = [
                'requiredtar' => $required,
                'achievedtar' => $achieved,
                'percentage' => round(($achieved / $required) * 100, 1),
                'display' => round(($achieved / $required) * 100)
            ];
        }


        $challanges = challanges::where('status', 'Pending')->inRandomOrder()->limit(5)->get();



        return view('dashboard', [
            'categories' => $days->map(fn($d) => Carbon::parse($d)->format('d')),
            'mobileData' => $mobileData,
            'walletData' => $walletData,
            'adslData' => $adslData,
            'landlineData' => $landlineData,
            'topUsers' => $topUsers,
            'challanges' => $challanges,
            'thismonth' => Carbon::now()->format('F, Y'),
            'percentages' => $percentages,
            'adslsData' => array_values($monthlyCounts->toArray())
        ]);
    }


    public function index(Request $request)
    {
        $month = $request->query('month', now()->format('Y-m'));
        $date = Carbon::createFromFormat('Y-m', $month);
        $startOfMonth = $date->copy()->startOfMonth()->toDateString();
        $endOfMonth = $date->copy()->endOfMonth()->toDateString();

        $days = collect(range(1, $date->daysInMonth))->map(fn($d) => $date->copy()->day($d)->toDateString());

        // Helper to build daily counts
        $dailyCounts = function ($table, $dateField, $filterField, $filterValues) use ($startOfMonth, $endOfMonth) {
            return DB::table($table)
                ->selectRaw("DATE($dateField) as date, COUNT(*) as total")
                ->when($filterField && $filterValues, function ($q) use ($filterField, $filterValues) {
                    $q->whereIn($filterField, $filterValues);
                })
                ->whereBetween($dateField, [$startOfMonth, $endOfMonth])
                ->groupBy(DB::raw("DATE($dateField)"))
                ->pluck('total', 'date'); // ['2025-05-01' => 4, ...]
        };

        $mobileCounts = $dailyCounts('mobiles', 'cashbox_date', 'contract_type', ['prepaid', 'postpaid', 'control']);
        $adslCounts   = $dailyCounts('adsl', 'cashbox_date', 'contract_type', ['New ADSL']);
        $landlineCounts = $dailyCounts('landline', 'cashbox_date', 'contract_type', ['New Landline']);

        $mobileData = $days->map(fn($d) => $mobileCounts[$d] ?? 0);
        $adslData = $days->map(fn($d) => $adslCounts[$d] ?? 0);
        $landlineData = $days->map(fn($d) => $landlineCounts[$d] ?? 0);




        return view('dashboard', [
            'categories' => $days->map(fn($d) => Carbon::parse($d)->format('d')),
            'mobileData' => $mobileData,
            'adslData' => $adslData,
            'landlineData' => $landlineData
        ]);
    }



    public function ChartData1()
    {


        // $mobilesPerMonth = DB::table('mobiles')
        //     ->selectRaw('MONTH(cashbox_date) as month, COUNT(*) as total')
        //     ->whereYear('cashbox_date', now()->year)
        //     ->whereIn('contract_type', ['Control', 'Prepaid', 'postpaid'])
        //     ->groupBy('month')
        //     ->orderBy('month')
        //     ->pluck('total', 'month')
        //     ->toArray();

        // // Ensure 12 months filled
        // $monthlyCounts = collect(range(1, 12))->mapWithKeys(function ($m) use ($mobilesPerMonth) {
        //     return [$m => $mobilesPerMonth[$m] ?? 0];
        // });

        // return response()->json(array_values($monthlyCounts->toArray()));

        $user = Auth::user();
        $location = $user->location_id;

        // Achieved mobiles per month
        $mobilesPerMonth = DB::table('mobiles')
            ->selectRaw('MONTH(cashbox_date) as month, COUNT(*) as total')
            ->whereYear('cashbox_date', now()->year)
            ->whereIn('contract_type', ['Control', 'Prepaid', 'postpaid'])
            ->where('location_id', $location)
            ->groupBy('month')
            ->pluck('total', 'month')
            ->toArray();

        // Targets per month
        $targetsPerMonth = DB::table('target_report')
            ->selectRaw('MONTH(date) as month, SUM(quantity) as total')
            ->whereYear('date', now()->year)
            ->where('type', 'Location_Target')
            ->where('quantity', '>', 0)
            ->where('location_id', $location)
            ->whereIn('category', ['Prepaid', 'Control', 'Postpaid'])
            ->groupByRaw('MONTH(date)')
            ->pluck('total', 'month')
            ->toArray();

        $achieved = [];
        $target = [];

        foreach (range(1, 12) as $month) {
            $achieved[] = $mobilesPerMonth[$month] ?? 0;
            $target[] = $targetsPerMonth[$month] ?? 0;
        }

        return response()->json([
            'achieved' => $achieved,
            'target' => $target,
        ]);
    }


    public function ChartData2()
    {

        $user = Auth::user();
        $location = $user->location_id;

        // $year = now()->year;

        // $rawData = DB::table('mobiles')
        //     ->selectRaw('MONTH(cashbox_date) as month, COUNT(*) as count')
        //     ->whereYear('cashbox_date', $year)
        //     ->whereIn('contract_type', ['Control', 'Prepaid'])
        //     ->groupBy(DB::raw('MONTH(cashbox_date)'))
        //     ->orderBy('month')
        //     ->get();

        // // Build an array for all 12 months, even if some are missing
        // $finalData = collect(range(1, 12))->map(function ($month) use ($rawData) {
        //     $entry = $rawData->firstWhere('month', $month);
        //     return [
        //         'day' => Carbon::create()->month($month)->format('F'),
        //         'Sales' => $entry ? $entry->count : 0
        //     ];
        // });


        // return response()->json($finalData);



        $mobilesPerMonth = DB::table('adsls')
            ->selectRaw('MONTH(cashbox_date) as month, COUNT(*) as total')
            ->whereYear('cashbox_date', now()->year)
            ->whereIn('contract_type', ['New ADSL'])
            ->where('location_id', $location)
            ->groupBy('month')
            ->orderBy('month')
            ->pluck('total', 'month')
            ->toArray();


        $targetsPerMonth = DB::table('target_report')
            ->selectRaw('MONTH(date) as month, SUM(quantity) as total')
            ->whereYear('date', now()->year)
            ->where('type', 'Location_Target')
            ->where('quantity', '>', 0)
            ->where('location_id', $location)
            ->where('category', 'ADSL')           
            ->groupByRaw('MONTH(date)')
            ->pluck('total', 'month')
            ->toArray();



        $achieved = [];
        $target = [];

        foreach (range(1, 12) as $month) {
            $achieved[] = $mobilesPerMonth[$month] ?? 0;
            $target[] = $targetsPerMonth[$month] ?? 0;
        }

        return response()->json([
            'achieved' => $achieved,
            'target' => $target,
        ]);


    }



    public function DashboardUserView()
    {
        $month = request('month', \Carbon\Carbon::now()->format('Y-m')); // Example full month string request('month', \Carbon\Carbon::now()->format('Y-m'));
        $date = Carbon::createFromFormat('Y-m', $month);
        $user = Auth::user();

        $datos = $user->id;
        $location = $user->location_id;
        $from = Carbon::now()->startOfYear()->toDateString();

        $startOfMonth = $date->copy()->startOfMonth()->toDateString();
        $endOfMonth = $date->copy()->endOfMonth()->toDateString();

        $days = collect(range(1, $date->daysInMonth))->map(fn($d) => $date->copy()->day($d)->toDateString());
        // Helper to build daily counts
        $dailyCounts = function ($table, $dateField, $filterField, $filterValues) use ($startOfMonth, $endOfMonth, $datos) {
            return DB::table($table)
                ->selectRaw("DATE($dateField) as date, COUNT(*) as total")
                ->when($filterField && $filterValues, function ($q) use ($filterField, $filterValues) {
                    $q->whereIn($filterField, $filterValues);
                })
                ->where('user_id', $datos)
                ->whereBetween($dateField, [$startOfMonth, $endOfMonth])
                ->groupBy(DB::raw("DATE($dateField)"))
                ->pluck('total', 'date'); // ['2025-05-01' => 4, ...]
        };

        $mobileCounts = $dailyCounts('mobiles', 'cashbox_date', 'contract_type', ['prepaid', 'postpaid', 'control']);
        $adslCounts   = $dailyCounts('adsls', 'cashbox_date', 'contract_type', ['New ADSL']);
        $walletCounts = $dailyCounts('mobiles', 'cashbox_date', 'contract_type', ['Wallet']);
        $landlineCounts = $dailyCounts('landlines', 'cashbox_date', 'contract_type', ['New Landline']);

        $mobileData = $days->map(fn($d) => $mobileCounts[$d] ?? 0);
        $walletData = $days->map(fn($d) => $walletCounts[$d] ?? 0);
        $adslData = $days->map(fn($d) => $adslCounts[$d] ?? 0);
        $landlineData = $days->map(fn($d) => $landlineCounts[$d] ?? 0);



        // Sum targets for the location (no user_id)
        $targets = DB::table('target_report')
            ->select(
                DB::raw('SUM(CASE WHEN category IN ("Control", "Postpaid", "Prepaid") THEN quantity ELSE 0 END) as mobile_target'),
                DB::raw('SUM(CASE WHEN category = "ADSL" THEN quantity ELSE 0 END) as adsl_target')
            )
            ->where('location_id', $location)
            ->where('type', 'Agent_Target')
            ->whereBetween('date', [$startOfMonth, $endOfMonth])
            ->first();

        // Subquery: mobile sales per user
        $mobileSalesSub = DB::table('mobiles')
            ->select('user_id', DB::raw('COUNT(id) as mobile_sales'))
            ->whereIn('contract_type', ['prepaid', 'postpaid', 'control'])
            ->whereBetween('cashbox_date', [$startOfMonth, $endOfMonth])
            ->where('location_id', $location)
            ->groupBy('user_id');

        // Subquery: adsl sales per user
        $adslSalesSub = DB::table('adsls')
            ->select('user_id', DB::raw('COUNT(id) as adsl_sales'))
            ->whereBetween('cashbox_date', [$startOfMonth, $endOfMonth])
            ->where('location_id', $location)
            ->groupBy('user_id');


        $totalTarget = ($targets->mobile_target ?? 0) + ($targets->adsl_target ?? 0);

        // Get users in location with sales joined
        $topUsers = DB::table('users')
            ->where('location_id', $location)->whereIn('users.role_name', ['Agent', 'Leader'])
            ->leftJoinSub($mobileSalesSub, 'mobile_sales', function ($join) {
                $join->on('users.id', '=', 'mobile_sales.user_id');
            })
            ->leftJoinSub($adslSalesSub, 'adsl_sales', function ($join) {
                $join->on('users.id', '=', 'adsl_sales.user_id');
            })
            ->select(
                'users.id',
                'users.name',
                'users.profile_pic',
                DB::raw('IFNULL(mobile_sales.mobile_sales, 0) as mobile_sales'),
                DB::raw('IFNULL(adsl_sales.adsl_sales, 0) as adsl_sales'),
                DB::raw("
            ROUND(
                (
                    (IFNULL(mobile_sales.mobile_sales, 0) + IFNULL(adsl_sales.adsl_sales, 0)) /
                    NULLIF($totalTarget, 0)
                ) * 100, 2
            ) as achievement_percent
        ")
            )
            ->orderByDesc('achievement_percent')
            ->limit(20)
            ->get();


        $adslsPerMonth = DB::table('adsls')
            ->selectRaw('MONTH(created_at) as month, COUNT(*) as total')
            ->whereYear('created_at', now()->year)
            ->groupBy('month')
            ->orderBy('month')
            ->pluck('total', 'month')
            ->toArray();

        // Fill missing months
        $monthlyCounts = collect(range(1, 12))->mapWithKeys(function ($m) use ($adslsPerMonth) {
            return [$m => $adslsPerMonth[$m] ?? 0];
        });


        // $location = $request->input('location', $location);
        $startDate = Carbon::now()->startOfMonth()->toDateString();
        $endDate = Carbon::now()->endOfMonth()->toDateString();

        // Helper query function
        $targetSum = fn($conditions) =>
        DB::table('target_report')->selectRaw('SUM(quantity) as total')
            ->whereBetween('date', [$startDate, $endDate])
            ->where('type', 'Agent_Target')
            ->where('quantity', '>', 0)

            ->where('location', $location)
            ->where($conditions)
            ->first()->total ?? 0;


        $countSum = fn($table, $conditions) =>
        DB::table($table)->selectRaw('COUNT(*) as total')
            ->whereBetween('cashbox_date', [$startDate, $endDate])
            ->where('user_id', $datos)
            ->where('location_id', $location)
            ->where($conditions)
            ->first()->total ?? 0;

        $targets = [
            'prepaid' => $targetSum(fn($q) => $q->whereIn('category', ['Prepaid', 'Control', 'Postpaid'])),
            'landline' => $targetSum(fn($q) => $q->where('category', 'Fixed')),
            'adsl' => $targetSum(fn($q) => $q->where('category', 'ADSL')),
            'wallet' => $targetSum(fn($q) => $q->where('category', 'Wallet')),
        ];



        $actuals = [
            'prepaid' => $countSum('mobiles', fn($q) => $q->whereIn('contract_type', ['Control', 'Prepaid', 'Postpaid'])),
            'landline' => $countSum('landlines', fn($q) => $q->where('contract_type', 'New Landline')),
            'adsl' => $countSum('adsls', fn($q) => $q->where('contract_type', 'New ADSL')),
            'wallet' => $countSum('mobiles', fn($q) => $q->where('contract_type', 'Wallet')),
        ];

        $percentages = [];
        foreach ($targets as $key => $val) {


            $required = max(1, round($val));

            $achieved = $actuals[$key];

            $percentages[$key] = [
                'requiredtar' => $required,
                'achievedtar' => $achieved,
                'percentage' => round(($achieved / $required) * 100, 1),
                'display' => round(($achieved / $required) * 100)
            ];
        }


        $challanges = challanges::where('status', 'Pending')->inRandomOrder()->limit(5)->get();
        $column = str_replace('.', '_', $user->username);

        $annualLeave = 0;
        $rows = DB::table('sched')
            ->select($column)
            ->whereBetween('date', [$from, $endOfMonth])
            ->whereRaw("SUBSTR(`$column`, -12) = 'Annual_Leave'")
            ->get();

        $parts = 0;

        foreach ($rows as $row) {
            $val = $row->$column;
            if (strpos($val, '|') !== false) {
                $parts = explode('|', $val);
                $annualLeave += isset($parts[1]) ? $parts[1] : 0;
            }
        }

        // Count for other leave types
        $leaveCounts = [
            'sick_leave' => DB::table('sched')
                ->whereBetween('date', [$from, $endOfMonth])
                ->where($column, 'Sick_Leave')->count(),

            'paternity_leave' => DB::table('sched')
                ->whereBetween('date', [$from, $endOfMonth])
                ->where($column, 'Paternity_Leave')->count(),

            'over_time' => DB::table('sched')
                ->whereBetween('date', [$from, $endOfMonth])
                ->where($column, 'like', '%Over_Time')->count(),

            'permission' => DB::table('sched')
                ->whereBetween('date', [$from, $endOfMonth])
                ->where($column, 'like', '%Permission')->count(),

            'emergency_leave' => DB::table('sched')
                ->whereBetween('date', [$from, $endOfMonth])
                ->whereRaw("SUBSTR(`$column`, -15) = 'Emergency_Leave'")
                ->count(),

            'late_attendance' => DB::table('logs')
                ->whereBetween('date', [$startOfMonth, $endOfMonth])
                ->where('comment', 'Late Attendance')
                ->where('user_id', $datos)
                ->count(),

        ];


        return view('dashboarduser', [
            'categories' => $days->map(fn($d) => Carbon::parse($d)->format('d')),
            'mobileData' => $mobileData,
            'walletData' => $walletData,
            'adslData' => $adslData,
            'landlineData' => $landlineData,
            'topUsers' => $topUsers,
            'challanges' => $challanges,
            'thismonth' => Carbon::now()->format('F, Y'),
            'percentages' => $percentages,
            'annual_leave' => $annualLeave,
            'emergency_leave' => $leaveCounts['emergency_leave'],
            'sick_leave' => $leaveCounts['sick_leave'],
            'paternity_leave' => $leaveCounts['paternity_leave'],
            'over_time' => $leaveCounts['over_time'],
            'permission' => $leaveCounts['permission'],
            'late_attendance' => $leaveCounts['late_attendance'],
            'adslsData' => array_values($monthlyCounts->toArray())
        ]);
    }
}
