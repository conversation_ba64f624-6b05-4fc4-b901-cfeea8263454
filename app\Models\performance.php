<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class performance extends Model
{

    protected $fillable = [
        'action_date',
        'title',
        'type',
        'upload',
        'comment',
        'location_id',
        'user_id',
        // add other fields if needed later
    ];







    public function users()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }


    public function location()
    {
        return $this->belongsTo(locations::class, 'location_id', 'id');
    }
}
