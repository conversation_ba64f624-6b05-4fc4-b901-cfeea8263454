<?php
use Illuminate\Support\Facades\DB;

use App\Http\Controllers\system_langsControler;
use App\Models\system_langs;

$lang = system_langs::all();
$arr = [];
b:
foreach ($lang as $product) {
    $arr['trans'][$product->lang_key] = $product->english;
}
$filstatus = '';
$street = '';

?>










<?php $__env->startSection('admincontent'); ?>






    <!--begin::Content-->
    <div id="kt_app_content" class="app-content px-lg-3">
        <!--begin::Content container-->
        <div id="kt_app_content_container" class="app-container container-fluid">
            <!--begin::Table-->
            <div class="app-toolbar-wrapper d-flex flex-stack flex-wrap gap-4 w-100">
                <!--begin::Page title-->
                <div class="page-title d-flex align-items-center gap-1 me-3">
                    <!--begin::Title-->
                    <h1
                        class="page-heading d-flex flex-column justify-content-center text-gray-900 lh-1 fw-bolder fs-2x my-0 me-5">
                        <?php echo app('translator')->get('translate.Mobile Contracts'); ?></h1>
                    <!--end::Title-->
                    <!--begin::Breadcrumb-->
                    <ul class="breadcrumb breadcrumb-separatorless fw-semibold">
                        &nbsp;&nbsp;&nbsp;
                        <li class="breadcrumb-item text-gray-700 fw-bold lh-1">
                            <a href="index.php" class="text-gray-500 text-hover-primary">
                                <i class="ki-duotone ki-home fs-3 text-gray-500 mx-n1"></i>
                            </a>
                        </li>
                        <!--end::Item-->
                        <!--begin::Item-->

                    </ul>
                    <!--end::Breadcrumb-->
                </div>
                <!--end::Page title-->
                <!--begin::Actions-->
                <div class="d-flex align-items-center gap-2 gap-lg-3 flex-shrink-0">

                    <a href="<?php echo e(route('newmobile')); ?>" class="btn btn-sm btn-success d-flex flex-center ms-3 px-4 py-3">
                        <i class="ki-duotone ki-plus-square fs-2">
                            <span class="path1"></span>
                            <span class="path2"></span>
                            <span class="path3"></span>
                        </i>
                        &nbsp;&nbsp;&nbsp;

                        <span> <?php echo app('translator')->get('translate.Add New'); ?></span>
                    </a>
                </div>
                <!--end::Actions-->
            </div>
            <!--end::Toolbar wrapper-->
            <br>
            <div class="card">
                <div class="card-header border-0 pt-6">
                    <!--begin::Card title-->
                    <div class="card-title">
                        <form action="<?php echo e(route('searchmobile')); ?>" id="form1" method="post">
                            <?php echo csrf_field(); ?>


                            <div class="d-flex align-items-center position-relative my-1">
                                <i class="ki-duotone ki-magnifier fs-3 position-absolute ms-5">
                                    <span class="path1"></span>
                                    <span class="path2"></span>
                                </i>
                                

                                <input type="text" class="form-control form-control-solid  ps-13"
                                    style="width: 100%;text-align: center;" placeholder=" <?php echo app('translator')->get('translate.Search'); ?>"
                                    name="searchcategory" value="<?php echo e($searchcat); ?>" />&nbsp;&nbsp;&nbsp;
                                <button type="submit" class="btn btn-primary">
                                    <i class="fa fa-search fs-2"></i></button>
                            </div>
                            <!--end::Search-->

                        </form>

                    </div>
                    <!--begin::Card title-->
                    <!--begin::Card toolbar-->
                    <div class="card-toolbar">
                        <!--begin::Toolbar-->
                        <div class="d-flex justify-content-end" data-kt-user-table-toolbar="base" id="bunsec">
                            <!--begin::Filter-->
                            <button type="button" class="btn btn-light-primary me-3" data-kt-menu-trigger="click"
                                data-kt-menu-placement="bottom-end">
                                <i class="ki-duotone ki-filter fs-2">
                                    <span class="path1"></span>
                                    <span class="path2"></span>
                                </i>&nbsp;&nbsp; <?php echo app('translator')->get('translate.Filter'); ?></button>
                            <!--begin::Menu 1-->
                            <div class="menu menu-sub menu-sub-dropdown w-300px w-md-325px" data-kt-menu="true">
                                <!--begin::Header-->
                                <div class="px-7 py-5">
                                    <div class="fs-5 text-gray-900 fw-bold"> <?php echo app('translator')->get('translate.Filter Options'); ?></div>
                                </div>
                                <!--end::Header-->
                                <!--begin::Separator-->
                                <div class="separator border-gray-200"></div>
                                <!--end::Separator-->
                                <!--begin::Content-->

                                <form action="lawers/index.php" id="form2" method="get">


                                    <div class="px-7 py-5" data-kt-user-table-filter="form" dir="ltr">
                                        <!--begin::Input group-->

                                        <div class="mb-10">
                                            <label class="form-label fs-6 fw-semibold"><?php echo app('translator')->get('translate.Date From'); ?>
                                                :</label>

                                            <input type="date" class="form-control form-control-solid" name="fildatefrom"
                                                value="" />

                                        </div>


                                        <div class="mb-10">
                                            <label class="form-label fs-6 fw-semibold"><?php echo app('translator')->get('translate.Date To'); ?>
                                                :</label>

                                            <input type="date" class="form-control form-control-solid" name="fildateto"
                                                value="" />

                                        </div>


                                        <!--begin::Input group-->
                                        <div class="mb-10">
                                            <label class="form-label fs-6 fw-semibold"><?php echo app('translator')->get('translate.Status'); ?>
                                                :</label>
                                            <select class="form-select form-select-solid fw-bold" data-kt-select2="true"
                                                data-placeholder="Select option" data-allow-clear="true" name="filstatus"
                                                data-kt-user-table-filter="two-step" data-hide-search="true">
                                                <option value="<?php echo $filstatus; ?>"><?php if (empty($filstatus)) {
                                                    $filstatust = $arr['trans']['Status'];
                                                    echo $filstatust;
                                                } else {
                                                    echo $arr['trans'][$filstatus];
                                                } ?></option>
                                                <option value="Active"><?php echo e($arr['trans']['Active']); ?></option>
                                                <option value="Suspend"><?php echo e($arr['trans']['Suspend']); ?></option>
                                            </select>
                                        </div>
                                        <!--end::Input group-->


                                        <!--begin::Actions-->
                                        <div class="d-flex justify-content-end">
                                            <button type="submit"
                                                class="btn btn-light btn-active-light-primary fw-semibold me-2 px-6"
                                                data-kt-menu-dismiss="true" name="reset" value="reset"
                                                data-kt-user-table-filter="reset"><?php echo app('translator')->get('translate.Reset'); ?></button>
                                            <button type="submit" class="btn btn-primary fw-semibold px-6"
                                                data-kt-menu-dismiss="true"
                                                data-kt-user-table-filter="filter"><?php echo app('translator')->get('translate.Apply'); ?></button>
                                        </div>
                                        <!--end::Actions-->
                                    </div>
                                    <!--end::Content-->

                                </form>
                            </div>

                            <button type="button" class="btn btn-light-primary me-3" id="modexport">
                                <i class="ki-duotone ki-exit-up fs-2">
                                    <span class="path1"></span>
                                    <span class="path2"></span>
                                </i>&nbsp;&nbsp; <?php echo app('translator')->get('translate.Export'); ?></button>

                            <button type="button" class="btn btn-primary" style="display: none;" id="appiontbutt"
                                data-bs-toggle="modal" data-bs-target="#kt_modal_add_user">
                                <i class="ki-duotone ki-plus fs-2"></i>Add New</button>&nbsp;&nbsp;&nbsp;

                            <button type="button" class="btn btn-danger" data-kt-user-table-select="delete_selected"
                                style="display:none; padding: 5px 5px;text-align: center;" id="delsel"
                                onclick="deleteall();">
                                &nbsp;

                                <i class="ki-duotone ki-delete-folder" style="font-size:25px;color:white;">
                                    <span class="path1"></span>
                                    <span class="path2"></span>
                                </i>

                            </button>

                        </div>

                        <div class="modal" style="display: none;" id="mymodcustwo" dir="ltr">
                            <!--begin::Modal dialog-->
                            <div class="modal-dialog modal-dialog-centered mw-650px">
                                <!--begin::Modal content-->
                                <div class="modal-content">
                                    <!--begin::Modal header-->
                                    <div class="modal-header">
                                        <!--begin::Modal title-->
                                        <h2 class="fw-bold"><?php echo app('translator')->get('translate.Export'); ?></h2>
                                        <!--end::Modal title-->
                                        <!--begin::Close-->
                                        <div class="btn btn-icon btn-sm btn-active-icon-primary closeex"
                                            data-kt-users-modal-action="close">
                                            <i class="ki-duotone ki-cross fs-1">
                                                <span class="path1"></span>
                                                <span class="path2"></span>
                                            </i>
                                        </div>
                                        <!--end::Close-->
                                    </div>
                                    <!--end::Modal header-->
                                    <!--begin::Modal body-->
                                    <div class="modal-body scroll-y mx-5 mx-xl-15 my-7">
                                        <!--begin::Form-->
                                        <form action="<?php echo e(route('exportmobile')); ?>" id="form3" method="post" role="form3"
                                            enctype='multipart/form-data'>
                                            <?php echo csrf_field(); ?>
                                            <div class="px-7 py-5" data-kt-user-table-filter="form" dir="ltr">
                                                <div class="mb-10">
                                                    <label class="form-label fs-6 fw-semibold"><?php echo app('translator')->get('translate.Date From'); ?>:</label>
                                                    <input type="date" class="form-control form-control-solid" name="date_from" value="" />
                                                </div>
                                                <div class="mb-10">
                                                    <label class="form-label fs-6 fw-semibold"><?php echo app('translator')->get('translate.Date To'); ?>:</label>
                                                    <input type="date" class="form-control form-control-solid" name="date_to" value="" />
                                                </div>
                                                <div class="d-flex justify-content-end">
                                                    <button type="button" class="btn btn-light btn-active-light-primary fw-semibold me-2 px-6" id="excancel"><?php echo app('translator')->get('translate.Cancel'); ?></button>
                                                    <button type="submit" class="btn btn-primary fw-semibold px-6"><?php echo app('translator')->get('translate.Export'); ?></button>
                                                </div>
                                            </div>
                                        </form>
                                        <!--end::Form-->
                                    </div>
                                    <!--end::Modal body-->
                                </div>
                                <!--end::Modal content-->
                            </div>
                            <!--end::Modal dialog-->
                        </div>
                        <!--end::Modal - New Card-->
                        <!--begin::Modal - Add task-->
                        <div class="modal fade" id="kt_modal_add_user" tabindex="-1" aria-hidden="true">
                            <!--begin::Modal dialog-->
                            <div class="modal-dialog modal-dialog-centered mw-650px">
                                <!--begin::Modal content-->
                                <div class="modal-content">
                                    <!--begin::Modal header-->
                                    <div class="modal-header" id="kt_modal_add_user_header">
                                        <!--begin::Modal title-->
                                        <h2 class="fw-bold"><?php echo app('translator')->get('translate.New Appointment'); ?></h2>
                                        <!--end::Modal title-->
                                        <!--begin::Close-->
                                        <div class="btn btn-icon btn-sm btn-active-icon-primary"
                                            data-kt-users-modal-action="close">
                                            <i class="ki-duotone ki-cross fs-1">
                                                <span class="path1"></span>
                                                <span class="path2"></span>
                                            </i>
                                        </div>
                                        <!--end::Close-->
                                    </div>
                                    <!--end::Modal header-->
                                    <!--begin::Modal body-->
                                    <div class="modal-body px-5 my-7">


                                        <form action="" id="kt_modal_add_user_form" method="post"
                                            enctype='multipart/form-data'>

                                            <!--begin::Form-->
                                            <!--begin::Scroll-->
                                            <div class="d-flex flex-column scroll-y px-5 px-lg-10"
                                                id="kt_modal_add_user_scroll" data-kt-scroll="true"
                                                data-kt-scroll-activate="true" data-kt-scroll-max-height="auto"
                                                data-kt-scroll-dependencies="#kt_modal_add_user_header"
                                                data-kt-scroll-wrappers="#kt_modal_add_user_scroll"
                                                data-kt-scroll-offset="300px">
                                                <!--begin::Input group-->

                                                <!--begin::Input group-->
                                                <div class="fv-row mb-7">
                                                    <!--begin::Label-->
                                                    <label
                                                        class="required fw-semibold fs-6 mb-2"><?php echo $arr['trans']['Select Template']; ?></label>
                                                    <!--end::Label-->
                                                    <!--begin::Input-->
                                                    <input type="text" name="cusappid" id="cusappid"
                                                        style="display: none;"
                                                        class="form-control form-control-solid mb-3 mb-lg-0" />
                                                    <!--end::Input-->


                                                    <select name="apptemplate" required data-placeholder="Select a format"
                                                        data-hide-search="true"
                                                        class="form-select form-select-solid fw-bold">

                                                        <option value=""><?php echo $arr['trans']['Select Template']; ?></option>
                                                        <option value="Video Call">Video Call (German)</option>
                                                        <option value="Hausbesuh">Hausbesuch (German)</option>

                                                    </select>



                                                </div>
                                                <!--end::Input group-->
                                                <!--begin::Input group-->
                                                <div class="fv-row mb-7">
                                                    <!--begin::Label-->
                                                    <label class="required fw-semibold fs-6 mb-2"><?php echo $arr['trans']['Date & Time']; ?>
                                                    </label>
                                                    <!--end::Label-->
                                                    <!--begin::Input-->
                                                    <input type="datetime-local" name="appdate" required id="appdate"
                                                        class="form-control form-control-solid mb-3 mb-lg-0" />
                                                    <!--end::Input-->
                                                </div>
                                                <!--end::Input group-->


                                                <!--begin::Input group-->
                                                <div class="fv-row mb-7">
                                                    <!--begin::Label-->
                                                    <label class="required fw-semibold fs-6 mb-2"><?php echo $arr['trans']['Duration']; ?>
                                                    </label>
                                                    <!--end::Label-->
                                                    <select name="appduration" required data-placeholder="Select a format"
                                                        data-hide-search="true"
                                                        class="form-select form-select-solid fw-bold">

                                                        <option value="">Select Duration </option>
                                                        <option value="10 Minutes">10 Minutes</option>
                                                        <option value="20 Minutes">20 Minutes</option>
                                                        <option value="30 Minutes">30 Minutes</option>
                                                        <option value="40 Minutes">40 Minutes</option>
                                                        <option value="50 Minutes">50 Minutes</option>
                                                        <option value="60 Minutes">60 Minutes</option>

                                                    </select>
                                                </div>
                                                <!--end::Input group-->


                                                <div class="fv-row mb-7">
                                                    <!--begin::Label-->
                                                    <label class="required fw-semibold fs-6 mb-2"><?php echo $arr['trans']['Address']; ?>
                                                    </label>
                                                    <!--end::Label-->
                                                    <!--begin::Input-->
                                                    <input type="text" name="appaddress" id="appaddress"
                                                        class="form-control form-control-solid mb-3 mb-lg-0"
                                                        value="<?php echo $street; ?>" />
                                                    <!--end::Input-->




                                                </div>
                                                <!--end::Input group-->




                                                <!--begin::Input group-->
                                                <div class="d-flex flex-column mb-8">
                                                    <label class="fs-6 fw-semibold mb-2"><?php echo $arr['trans']['Comments']; ?></label>
                                                    <textarea class="form-control form-control-solid" rows="4" name="commentsapp" placeholder="Details"></textarea>
                                                </div>


                                            </div>
                                            <!--end::Scroll-->
                                            <!--begin::Actions-->
                                            <div class="text-center pt-10">
                                                <button type="reset" class="btn btn-light me-3"
                                                    data-kt-users-modal-action="cancel">Discard</button>
                                                <button style="display: none" type="submit" class="btn btn-primary"
                                                    data-kt-users-modal-action="submit">
                                                    <span class="indicator-label">Submit</span>
                                                    <span class="indicator-progress">Please wait...
                                                        <span
                                                            class="spinner-border spinner-border-sm align-middle ms-2"></span></span>
                                                </button>

                                                <button type="submit" class="btn btn-primary" name="submitapp">
                                                    <span class="indicator-label"><?php echo $arr['trans']['Save']; ?></span>
                                                    <span class="indicator-progress">Please wait...
                                                        <span
                                                            class="spinner-border spinner-border-sm align-middle ms-2"></span></span>
                                                </button>


                                            </div>
                                        </form>
                                        <!--end::Form-->
                                        <!--end::Form-->
                                    </div>
                                    <!--end::Modal body-->
                                </div>
                                <!--end::Modal content-->
                            </div>
                            <!--end::Modal dialog-->
                        </div>
                        <!--end::Modal - Add task-->
                    </div>
                    <!--end::Card toolbar-->
                </div>
                <!--end::Card header-->




                <!--begin::Card body-->
                <div class="card-body py-4">
                    <div class="table-responsive">


                        <table class="table align-middle table-row-bordered table-row-solid gy-4 gs-9 table-bordered" dir="<?php echo e(app()->getLocale() === 'ar' ? 'rtl' : 'ltr'); ?>">
                            <!--begin::Thead-->
                            <thead class="table-light">


                                <tr class="text-start text-muted fw-bold fs-7 text-uppercase gs-0">
                                    <th class="w-10px pe-2">
                                        <div class="form-check form-check-sm form-check-custom form-check-solid me-3">
                                            <input class="form-check-input" type="checkbox" name="select-all"
                                                id="all" value="all" onclick="test(this);" />


                                        </div>
                                    </th>



                                    <th class="min-w-50px dirside" onclick="numberTableSort(this,true,'en')">
                                        <?php echo app('translator')->get('translate.Date'); ?></th>
                                    <th class="min-w-100px dirside" onclick="numberTableSort(this,true,'en')">
                                        <?php echo app('translator')->get('translate.Product SKU'); ?></th>
                                    <th class="min-w-50px dirside" onclick="numberTableSort(this,true,'en')">
                                        <?php echo app('translator')->get('translate.Number'); ?></th>
                                    <th class="min-w-50px dirside" onclick="numberTableSort(this,true,'en')">
                                        <?php echo app('translator')->get('translate.Package'); ?></th>
                                    <th class="min-w-110px dirside" onclick="numberTableSort(this,true,'en')">
                                        <?php echo app('translator')->get('translate.Product'); ?></th>
                                    <th class="min-w-50px dirside" onclick="numberTableSort(this,true,'en')">
                                        <?php echo app('translator')->get('translate.Contract Type'); ?></th>

                                    <th class="min-w-50px dirside" onclick="numberTableSort(this,true,'en')">
                                        <?php echo app('translator')->get('translate.AddOns'); ?></th>

                                    <th class="min-w-50px dirside" onclick="numberTableSort(this,true,'en')">
                                        <?php echo app('translator')->get('translate.Username'); ?></th>
                                    <th class="min-w-50px dirside" onclick="numberTableSort(this,true,'en')">
                                        <?php echo app('translator')->get('translate.Location'); ?></th>

                                    <th class="text-end min-w-20px dirside1"><?php echo app('translator')->get('translate.Actions'); ?></th>
                                </tr>
                            </thead>

                            <tbody class="text-gray-600 fw-semibold">
                                <!-- Data will be loaded here -->


                                <?php $__currentLoopData = $contratcs; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $contratc): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td>
                                            <div class="form-check form-check-sm form-check-custom form-check-solid">
                                                <input class="form-check-input" type="checkbox" id="con_<?php echo e($contratc->id); ?>"
                                                    name="select" value="<?php echo e($contratc->id); ?>" onclick="test(this);" />
                                            </div>
                                        </td>


                                        <td><?php echo e($contratc->cashbox_date); ?></td>
                                        <td>

                                            <div class="d-flex flex-column">

                                                <h6 style="font-size: 12px"><span style="color: #b90b96">
                                                        <?php echo e($contratc->product_sku); ?></span></h6>

                                            </div>

                                        </td>
                                        <td><?php echo e($contratc->number); ?></td>


                                        <td><?php echo e($contratc->packages->package_name); ?></td>
                                        <td><?php echo e($contratc->products->Product_name); ?></td>
                                        <td><?php echo e($contratc->contract_type); ?></td>



                                        <td><?php echo e($contratc->addons); ?></td>

                                        <td cope="row" data-label="Username">
                                            <div class="d-flex flex-column">

                                                <h6 style="font-size: 12px"><span
                                                        style="color: #000099"><?php echo e($contratc->users->nick_name); ?></span>
                                                </h6>

                                            </div>
                                        </td>

                                        <td><?php echo e($contratc->location->name_en); ?></td>




                                        <td class="text-end" data-kt-filemanager-table="action_dropdown">
                                            <div class="d-flex justify-content-end">
                                                <!--begin::More-->
                                                <div class="ms-2">
                                                    <button type="button"
                                                        class="btn btn-sm btn-icon btn-light btn-active-light-primary "
                                                        data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end">
                                                        <i class="ki-duotone ki-dots-square">
                                                            <span class="path1"></span>
                                                            <span class="path2"></span>
                                                            <span class="path3"></span>
                                                            <span class="path4"></span>
                                                        </i>
                                                    </button>



                                                    <?php if(in_array(Auth::user()->role_name, ['Senior', 'Supervisor', 'Admins'])): ?>
                                                        <!--begin::Menu-->
                                                        <div class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-semibold fs-7 w-150px py-4"
                                                            data-kt-menu="true">
                                                            <!--begin::Menu item-->
                                                            <div class="menu-item px-3">
                                                                <a href="/cashier/editcashier/<?php echo e($contratc->id); ?>"
                                                                    class="menu-link px-3"><?php echo app('translator')->get('translate.Edit'); ?></a>
                                                            </div>
                                                            <!--end::Menu item-->
                                                            <!--begin::Menu item-->
                                                            <div class="menu-item px-3">
                                                                <a href="/deletemobile/<?php echo e($contratc->id); ?>"
                                                                    class="menu-link text-danger px-3"
                                                                    data-kt-filemanager-table-filter="delete_row">Delete</a>
                                                            </div>
                                                            <!--end::Menu item-->
                                                        </div>
                                                        <!--end::Menu-->
                                                    <?php else: ?>
                                                        <?php if($contratc->cashbox_date == date('Y-m-d')): ?>
                                                            <!--begin::Menu item-->
                                                            <div class="menu-item px-3">
                                                                <a href="/cashier/editcashier/<?php echo e($contratc->id); ?>"
                                                                    class="menu-link px-3"><?php echo app('translator')->get('translate.Edit'); ?></a>
                                                            </div>
                                                            <!--end::Menu item-->
                                                            <!--begin::Menu item-->
                                                            <div class="menu-item px-3">
                                                                <a href="/deletecashier/<?php echo e($contratc->id); ?>"
                                                                    class="menu-link text-danger px-3"
                                                                    data-kt-filemanager-table-filter="delete_row">Delete</a>
                                                            </div>
                                                            <!--end::Menu item-->
                                                        <?php endif; ?>
                                                    <?php endif; ?>

                                                    <!--end::More-->
                                                </div>
                                            </div>
                                        </td>


                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                            </tbody>
                        </table>
                        <!--end::Table-->


                    </div>


                    <?php echo e($contratcs->links()); ?>

                    <style>
                        svg {

                            height: 20px !important;
                        }

                        .hidden {
                            padding: 10px !important;
                        }

                        /* .dirside {
                    background-color:#b355b7 !important; text-align:center !important; color:#FFF !important
                } */
                    </style>


                </div>
                <!--end::Card body-->
            </div>
            <!--end::Card-->
        </div>
        <!--end::Content container-->
    </div>
    <!--end::Content-->
    </div>

    </div>
    <!--end:::Main-->
    </div>
    <!--end::Wrapper-->
    </div>
    <!--end::Page-->
    </div>
    <!--end::App-->
<?php $__env->stopSection(); ?>


<?php $__env->startSection('adminscripts'); ?>

    <script type="text/javascript">
        function test(event) {

            var alld1 = document.getElementById("delsel");
            var bunsec = document.getElementById("bunsec");
            var valuidd = event.id;

            if (valuidd == 'all') {

                if (document.getElementById("all").checked) {
                    $(':checkbox').each(function() {
                        this.checked = true;

                        alld1.style.display = "block";
                        bunsec.style.display = "none";

                    });
                } else {

                    $(':checkbox').each(function() {
                        this.checked = false;

                        alld1.style.display = "none";

                        bunsec.style.display = "block";
                    });
                }

            } else {


                if (document.getElementById(valuidd).checked) {
                    this.checked = true;

                    alld1.style.display = "block";
                    bunsec.style.display = "none";


                } else {

                    this.checked = false;

                    alld1.style.display = "none";

                    bunsec.style.display = "block";

                }

            }

        }


        function deleteall() {

            $(':checkbox').each(function() {

                if (this.checked == true) {

                    var checkid = this.id;

                    if (checkid == "" || checkid == "all") {} else {

                        var my_arr2 = checkid.split("_");

                        var valid = my_arr2[1];

                        $.ajax({
                            url: '/deletemobile/'+ valid,
                            success: function(data) {
                                var my_arr = data;

                                // if (my_arr == 'Done') {


                                //     testcon();

                                // } else {


                                //     alert('Customer Have Records On Contracts');



                                // }

                            }
                        });
                    }
                }
            });

        }
    </script>
    <script>
        document.getElementById('modexport').addEventListener('click', function() {
            document.getElementById('mymodcustwo').style.display = 'block';
        });

        document.querySelector('.closeex').addEventListener('click', function() {
            document.getElementById('mymodcustwo').style.display = 'none';
        });

        document.getElementById('excancel').addEventListener('click', function() {
            document.getElementById('mymodcustwo').style.display = 'none';
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.master_admin', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\xampp\htdocs\wetool\resources\views/mobile/mobileview.blade.php ENDPATH**/ ?>