<table>
    <thead>
        <tr>
            <th>ID</th>
            <th>SKU</th>
            <th>Number</th>
            <th>Product Name</th>
            <th>Price</th>
            <th>Package</th>
            <th>Username</th>
            <th>Cashbox Date</th>
            <th>Created Date</th>
            <th>Location</th>
        </tr>
    </thead>
    <tbody>
        @foreach($devices as $device)
            <tr>
                <td>{{ $device->id }}</td>
                <td>{{ $device->product_sku }}</td>
                <td>{{ $device->number }}</td>
                <td>{{ $device->product_name }}</td>
                <td>{{ $device->price }}</td>
                <td>{{ $device->packages->package_name ?? 'N/A' }}</td>
                <td>{{ $device->users->nick_name ?? $device->users->username ?? 'N/A' }}</td>
                <td>{{ $device->cashbox_date ?? 'N/A' }}</td>
                <td>{{ $device->created_at }}</td>
                <td>{{ $device->locationval->name_en ?? $device->location_id }}</td>
            </tr>
        @endforeach
    </tbody>
</table>