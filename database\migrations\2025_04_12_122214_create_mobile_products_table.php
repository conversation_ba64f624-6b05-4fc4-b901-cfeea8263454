<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('mobile_products', function (Blueprint $table) {
            $table->id();



            $table->string('Product_name')->nullable();
            $table->string('sku1')->nullable();
            $table->string('sku2')->nullable();
            $table->string('sku3')->nullable();
            $table->string('product_type')->nullable();
            $table->string('category')->nullable();
            $table->float('price', 1, 2)->nullable();


            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('mobile_products');
    }
};
