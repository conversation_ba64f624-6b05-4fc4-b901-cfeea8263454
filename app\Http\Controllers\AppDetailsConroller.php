<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\locations;
use App\Models\challanges;
use App\Models\location_machines;
use App\Models\app_details;
use App\Models\time_zone;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Config;

class AppDetailsConroller extends Controller
{


// -----------------------------------------  Challange Start --------------------------------------\\


    public function ChallengesView()
    {

        $allchallages = challanges::orderBy('created_at', 'desc')->get();

        return view('challanges', ['challenges' => $allchallages, 'searchcat' => '']);
    }



    public function ChallengeStore(Request $request)
    {
        $user = Auth::user();
        $username = $user->email;

        if ($request->memberid) {


            $perform = challanges::find($request->memberid);
            $perform->title = $request->title;
            $perform->type = $request->type;
            $perform->comment = $request->comment;
            $perform->user_id  = $user->id;
            $perform->location_id  = $user->location_id;




            if ($request->hasFile('upfile') && $request->file('upfile')->isValid()) {
                $image = $request->file('upfile');

                // Generate a unique name for the image
                $uniqueName = Str::random(20) . '.' . $image->getClientOriginalExtension();

                // Store the image in the 'images' folder of 'public' disk
                $path = $image->storeAs('upload', $uniqueName, 'public');

                $perform->profile_pic = $path;
            }


            $perform->save();


        } else {


            $perform = new challanges;
            $perform->title = $request->title;
            $perform->type = $request->type;
            $perform->comment = $request->comment;
            $perform->user_id  = $user->id;
            $perform->location_id  = $user->location_id;

            if ($request->hasFile('upfile') && $request->file('upfile')->isValid()) {
                $image = $request->file('upfile');

                // Generate a unique name for the image
                $uniqueName = Str::random(20) . '.' . $image->getClientOriginalExtension();

                // Store the image in the 'images' folder of 'public' disk
                $path = $image->storeAs('upload', $uniqueName, 'public');
                $perform->upload = $path;
            }


            $perform->save();
        }

        return redirect()->back()->with('success', 'Product added to cart!');
    }

// ------------------------------  Challange End  ----------------------------------\\


    public function LocationsView()
    {

        $alllocations = locations::orderBy('created_at', 'desc')->paginate(30);

        return view('locations', ['locations' => $alllocations, 'searchcat' => '']);
    }

    public function SettingView()
    {

        $adddetails = app_details::all();
        $timezones = time_zone::all();

        $todaydate = date("H:i:s");



        return view('tools/setting', ['appdata' => $adddetails, 'timezones' => $timezones ,'todaydate' => $todaydate]);
    }






    public function StoteAdimGeneral(Request $request)
    {


        if ($request->meta_title) {

            DB::table('app_details')->updateOrInsert(
                ['section' => 'Meta Title'], // Conditions to check
                [
                    'section' => 'Meta Title',
                    'note' => $request->meta_title,
                    'created_at' => now(),
                    'updated_at' => now()
                ]
            );
        }

        if ($request->meta_description) {

            DB::table('app_details')->updateOrInsert(
                ['section' => 'Meta Tag'], // Conditions to check
                [
                    'section' => 'Meta Tag',
                    'note' => $request->meta_description,
                    'created_at' => now(),
                    'updated_at' => now()
                ]
            );
        }


        if ($request->meta_keywords) {

            DB::table('app_details')->updateOrInsert(
                ['section' => 'Meta Keywords'], // Conditions to check
                [
                    'section' => 'Meta Keywords',
                    'note' => $request->meta_keywords,
                    'created_at' => now(),
                    'updated_at' => now()
                ]
            );
        }



        $adddetails = app_details::all();

        return redirect()->route('settingview')->with(['appdata' => $adddetails]);
    }


    public function StoteAdimStore(Request $request)
    {

        if ($request->store_name) {

            DB::table('app_details')->updateOrInsert(
                ['section' => 'Store Name'], // Conditions to check
                [
                    'section' => 'Store Name',
                    'note' => $request->store_name,
                    'created_at' => now(),
                    'updated_at' => now()
                ]
            );
        }
        if ($request->store_owner) {

            DB::table('app_details')->updateOrInsert(
                ['section' => 'Store Owner'], // Conditions to check
                [
                    'section' => 'Store Owner',
                    'note' => $request->store_owner,
                    'created_at' => now(),
                    'updated_at' => now()
                ]
            );
        }


        if ($request->store_address) {

            DB::table('app_details')->updateOrInsert(
                ['section' => 'Store Address'], // Conditions to check
                [
                    'section' => 'Store Address',
                    'note' => $request->store_address,
                    'created_at' => now(),
                    'updated_at' => now()
                ]
            );
        }


        if ($request->store_geocode) {

            DB::table('app_details')->updateOrInsert(
                ['section' => 'Store Code'], // Conditions to check
                [
                    'section' => 'Store Code',
                    'note' => $request->store_geocode,
                    'created_at' => now(),
                    'updated_at' => now()
                ]
            );
        }
        if ($request->store_email) {

            DB::table('app_details')->updateOrInsert(
                ['section' => 'Store Email'], // Conditions to check
                [
                    'section' => 'Store Email',
                    'note' => $request->store_email,
                    'created_at' => now(),
                    'updated_at' => now()
                ]
            );
        }


        if ($request->store_phone) {

            DB::table('app_details')->updateOrInsert(
                ['section' => 'Store Phone'], // Conditions to check
                [
                    'section' => 'Store Phone',
                    'note' => $request->store_phone,
                    'created_at' => now(),
                    'updated_at' => now()
                ]
            );
        }

        if ($request->store_fax) {

            DB::table('app_details')->updateOrInsert(
                ['section' => 'Store Fax'], // Conditions to check
                [
                    'section' => 'Store Fax',
                    'note' => $request->store_fax,
                    'created_at' => now(),
                    'updated_at' => now()
                ]
            );
        }

        if ($request->facebook) {

            DB::table('app_details')->updateOrInsert(
                ['section' => 'Facebook'], // Conditions to check
                [
                    'section' => 'Facebook',
                    'note' => $request->facebook,
                    'created_at' => now(),
                    'updated_at' => now()
                ]
            );
        }

        if ($request->twitter) {

            DB::table('app_details')->updateOrInsert(
                ['section' => 'Store Twitter'], // Conditions to check
                [
                    'section' => 'Store Twitter',
                    'note' => $request->twitter,
                    'created_at' => now(),
                    'updated_at' => now()
                ]
            );
        }


        if ($request->linkdin) {

            DB::table('app_details')->updateOrInsert(
                ['section' => 'Store Linkdin'], // Conditions to check
                [
                    'section' => 'Store Linkdin',
                    'note' => $request->linkdin,
                    'created_at' => now(),
                    'updated_at' => now()
                ]
            );
        }

        if ($request->youtube) {

            DB::table('app_details')->updateOrInsert(
                ['section' => 'Store Youtube'], // Conditions to check
                [
                    'section' => 'Store Youtube',
                    'note' => $request->youtube,
                    'created_at' => now(),
                    'updated_at' => now()
                ]
            );
        }

        if ($request->instagram) {

            DB::table('app_details')->updateOrInsert(
                ['section' => 'Store Instagram'], // Conditions to check
                [
                    'section' => 'Store Instagram',
                    'note' => $request->instagram,
                    'created_at' => now(),
                    'updated_at' => now()
                ]
            );
        }


        $adddetails = app_details::all();

        $todaydate = date("H:i:s");


        return redirect()->route('settingview')->with(['appdata' => $adddetails,'todaydate' => $todaydate]);
    }



    public function StoteAdimLocation(Request $request)
    {

        if ($request->localization_country) {

            DB::table('app_details')->updateOrInsert(
                ['section' => 'Store Country'], // Conditions to check
                [
                    'section' => 'Store Country',
                    'note' => $request->localization_country,
                    'created_at' => now(),
                    'updated_at' => now()
                ]
            );
        }
        if ($request->localization_language) {

            DB::table('app_details')->updateOrInsert(
                ['section' => 'Store Language'], // Conditions to check
                [
                    'section' => 'Store Language',
                    'note' => $request->localization_language,
                    'created_at' => now(),
                    'updated_at' => now()
                ]
            );
        }


        if ($request->localization_currency) {

            DB::table('app_details')->updateOrInsert(
                ['section' => 'Store Currency'], // Conditions to check
                [
                    'section' => 'Store Currency',
                    'note' => $request->localization_currency,
                    'created_at' => now(),
                    'updated_at' => now()
                ]
            );
        }
        if ($request->timezones) {

            DB::table('app_details')->updateOrInsert(
                ['section' => 'Store TimeZone'], // Conditions to check
                [
                    'section' => 'Store TimeZone',
                    'note' => $request->timezones,
                    'created_at' => now(),
                    'updated_at' => now()
                ]
            );


        }





        $adddetails = app_details::all();

        return redirect()->route('settingview')->with(['appdata' => $adddetails]);
    }





    public function StoteAdimLogo(Request $request)
    {



        if ($request->hasFile('liteimage') && $request->file('liteimage')->isValid()) {
            $image = $request->file('liteimage');

            // Generate a unique name for the image
            $uniqueName = Str::random(20) . '.' . $image->getClientOriginalExtension();

            // Store the image in the 'images' folder of 'public' disk
            $path = $image->storeAs('logos', $uniqueName, 'public');


            DB::table('app_details')->updateOrInsert(
                ['section' => 'Light Logo'], // Conditions to check
                [
                    'section' => 'Light Logo',
                    'note' => $path,
                    'created_at' => now(),
                    'updated_at' => now()
                ]
            );
        }


        if ($request->hasFile('darkimage') && $request->file('darkimage')->isValid()) {
            $image = $request->file('darkimage');

            // Generate a unique name for the image
            $uniqueName = Str::random(20) . '.' . $image->getClientOriginalExtension();

            // Store the image in the 'images' folder of 'public' disk
            $path = $image->storeAs('logos', $uniqueName, 'public');


            DB::table('app_details')->updateOrInsert(
                ['section' => 'Dark Logo'], // Conditions to check
                [
                    'section' => 'Dark Logo',
                    'note' => $path,
                    'created_at' => now(),
                    'updated_at' => now()
                ]
            );
        }




        if ($request->hasFile('taskicon') && $request->file('taskicon')->isValid()) {
            $image = $request->file('taskicon');

            // Generate a unique name for the image
            $uniqueName = Str::random(20) . '.' . $image->getClientOriginalExtension();

            // Store the image in the 'images' folder of 'public' disk
            $path = $image->storeAs('logos', $uniqueName, 'public');


            DB::table('app_details')->updateOrInsert(
                ['section' => 'Task Icon'], // Conditions to check
                [
                    'section' => 'Task Icon',
                    'note' => $path,
                    'created_at' => now(),
                    'updated_at' => now()
                ]
            );
        }


        $adddetails = app_details::all();



        return redirect()->route('settingview')->with(['appdata' => $adddetails]);
    }







    public function StoreLocation(Request $request)
    {

        $user = Auth::user();
        $username = $user->email;
        $todatdate = date('Y-m-d');
        if ($request->cusappid) {

            $request->validate([
                'name_en' => ['required', 'max:255'],
                'location_type' => 'required',

            ]);


            $currectlandline = locations::find($request->cusappid);

            $currectlandline->name_en = $request->name_en;
            $currectlandline->name_ar = $request->name_ar;
            $currectlandline->location_type  = $request->location_type;
            $currectlandline->location_code  = $request->location_code;
            $currectlandline->direct_manager  = $request->direct_manager;
            $currectlandline->division  = $request->division;
            $currectlandline->area  = $request->area;
            $currectlandline->store_from  = $request->store_from;
            $currectlandline->store_to  = $request->store_to;

            $currectlandline->am_shift  = $request->am_shift;
            $currectlandline->pm_shift  = $request->pm_shift;
            $currectlandline->bw_shift  = $request->bw_shift;
            $currectlandline->fullshift_shift  = $request->fullshift_am;
            $currectlandline->regular_shift  = $request->regular_shift;
            $currectlandline->fullshift_am  = $request->fullshift_am;
            $currectlandline->fullshift_pm  = $request->fullshift_pm;


            $currectlandline->save();
        } else {

            $request->validate([
                'name_en' => ['required', 'max:255'],
                'location_type' => 'required',

            ]);


            $user = Auth::user();




            $currectlandline = new locations;
            $currectlandline->name_en = $request->name_en;
            $currectlandline->name_ar = $request->name_ar;
            $currectlandline->location_type  = $request->location_type;
            $currectlandline->location_code  = $request->location_code;
            $currectlandline->direct_manager  = $request->direct_manager;
            $currectlandline->division  = $request->division;
            $currectlandline->area  = $request->area;
            $currectlandline->store_from  = $request->store_from;
            $currectlandline->store_to  = $request->store_to;
            $currectlandline->am_shift  = $request->am_shift;
            $currectlandline->pm_shift  = $request->pm_shift;
            $currectlandline->bw_shift  = $request->bw_shift;
            $currectlandline->fullshift_shift  = $request->fullshift_am;
            $currectlandline->regular_shift  = $request->regular_shift;
            $currectlandline->fullshift_am  = $request->fullshift_am;
            $currectlandline->fullshift_pm  = $request->fullshift_pm;


            $currectlandline->save();
        }


        return redirect()->back()->with('success', 'Your action was successful!');
    }


    public function StoreManager()
    {

        $storemanagers = location_machines::orderBy('created_at', 'desc')->paginate(30);

        $locations = locations::all();


        return view('storemanager', ['storemanagers' => $storemanagers, 'searchcat' => '', 'locations' => $locations]);
    }



    public function StoreDevice(Request $request)
    {

        $user = Auth::user();
        $username = $user->id;
        $todatdate = date('Y-m-d');
        if ($request->cusappid) {

            $request->validate([
                'up_name' => ['required', 'max:255'],
                'up_model' => 'required',

            ]);


            $currectlandline = location_machines::find($request->cusappid);
            $currectlandline->name = $request->up_name;
            $currectlandline->model = $request->up_model;
            $currectlandline->serial  = $request->up_serial;
            $currectlandline->ip  = $request->up_ip;
            $currectlandline->type  = $request->up_type;
            $currectlandline->comment  = $request->up_comment;
            $currectlandline->status  = $request->up_status;
            $currectlandline->location_id  = $request->up_location;

            $currectlandline->save();

        } else {

            $request->validate([
                'name' => ['required', 'max:255'],
                'model' => 'required',

            ]);




            $currectlandline = new location_machines;
            $currectlandline->name = $request->name;
            $currectlandline->model = $request->model;
            $currectlandline->serial  = $request->serial;
            $currectlandline->ip  = $request->ip;
            $currectlandline->type  = $request->type;
            $currectlandline->comment  = $request->comment;
            $currectlandline->status  = $request->status;
            $currectlandline->location_id  = $request->location;
            $currectlandline->user_id  = $username;

            $currectlandline->save();
        }


        return redirect()->back()->with('success', 'Your action was successful!');
    }




}
