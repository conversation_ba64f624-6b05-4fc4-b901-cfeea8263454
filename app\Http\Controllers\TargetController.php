<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use App\Models\locations;
use App\Models\User;
use App\Models\target_report;




class TargetController extends Controller
{
    public function index()
    {
        $user = Auth::user();
        $year = date('Y');
        $month = date('M');

        $todatdate = Carbon::createFromDate($year, date('m', strtotime($month)), 1)->format('Y-m-d');

        if (!in_array($user->title, ['Management', 'Supervisor'])) {
            return view('errors.403'); // Create a 403 access denied view
        }

        $locations     = locations::all();

        $packages = DB::table('mobile_packages')->whereNotNull('num')->orderBy('num', 'ASC')->orderBy('id')->get();


        $targetReports = target_report::where('location_id', $user->location_id)
            ->where('date', $todatdate) // optional filter
            ->where('type', 'Location_Target') // optional filter
            ->get()
            ->keyBy('package_id');
        return view('target.index', compact('user', 'year', 'month', 'locations', 'packages', 'targetReports'));
    }

    public function store(Request $request)
    {
        // $request->validate([
        //     'location' => 'required',
        //     'month' => 'required',
        //     'year' => 'required',
        //     'agent' => 'required|numeric|min:1',
        // ]);


        $date = Carbon::createFromDate($request->year, $request->month, 1)->format('Y-m-d');

//    dd($date);

        $agentCount = (int) $request->agent;
        $a = (int) $request->a;
        $b = (int) $request->b;
        $c = (int) $request->c;
        $d = (int) $request->d;

        $agnum = ($a > 0 ? $a / 1 : 0) + ($b > 0 ? $b / 2 : 0) + ($c > 0 ? $c / 3 : 0) + ($d > 0 ? $d / 4 : 0);
        if ($agnum == 0) return back()->with('error', 'Please select agent numbers.');

        $packages = DB::table('mobile_packages')->whereNotNull('num')->get();

        foreach ($packages as $package) {
            $quantity = (int) $request->input($package->id);

            if ($package->package_name == 'We Wallet') {
                $agentTarget = ceil($quantity / $agentCount);
            } else {
                $agentTarget = ceil($quantity / $agnum);
            }

            $exists = DB::table('target_report')->where([
                ['date', $date],
                ['package', $package->package_name],
                ['location', $request->location]
            ])->exists();

            if ($exists) {
                DB::table('target_report')
                    ->where([
                        ['date', $date],
                        ['package', $package->package_name],
                        ['type', 'Location_Target'],
                        ['location', $request->location]
                    ])
                    ->update(['quantity' => $quantity]);

                DB::table('target_report')
                    ->where([
                        ['date', $date],
                        ['package', $package->package_name],
                        ['type', 'Agent_Target'],
                        ['location', $request->location]
                    ])
                    ->update(['quantity' => $agentTarget]);
            } else {


                DB::table('target_report')->insert([
                    'date' => $date,
                    'package' => $package->package_name,
                    'package_id' => $package->id,
                    'quantity' => $quantity,
                    'category' => $package->package_type,
                    'type' => 'Location_Target',
                    'location' => $request->location,
                    'location_id' => $request->location,
                    'user_name' => Auth::user()->username,
                    'user_id' => Auth::user()->id,
                ]);


                DB::table('target_report')->insert([
                    'date' => $date,
                    'package' => $package->package_name,
                    'package_id' => $package->id,
                    'quantity' => $agentTarget,
                    'category' => $package->package_type,
                    'type' => 'Agent_Target',
                    'location' => $request->location,
                    'location_id' => $request->location,
                    'user_name' => Auth::user()->username,
                    'user_id' => Auth::user()->id,
                ]);
            }
        }

        return redirect()->route('target.index')->with('success', 'Target data saved.');
    }



    public function showReport(Request $request)
    {


        $user = Auth::user();

        $datos = $user->id;
        $location = $user->location_id;
        $title = $user->title;
        $rank = $user->rank;



        $starget = match ($rank) {
            'A', null => 1,
            'B' => 2,
            'C' => 3,
            'D' => 4,
            default => 1,
        };

        // $location = $request->input('location', $location);
        $startDate = Carbon::now()->startOfMonth()->toDateString();
        $endDate = Carbon::now()->endOfMonth()->toDateString();

        // Helper query function
        $targetSum = fn($conditions) =>
        DB::table('target_report')->selectRaw('SUM(quantity) as total')
            ->whereBetween('date', [$startDate, $endDate])
            ->where('type', 'Agent_Target')
            ->where('location', $location)
            ->where($conditions)
            ->first()->total ?? 0;


        $countSum = fn($table, $conditions) =>
        DB::table($table)->selectRaw('COUNT(*) as total')
            ->whereBetween('cashbox_date', [$startDate, $endDate])
            ->where('user_id', $datos)
            ->where('location_id', $location)
            ->where($conditions)
            ->first()->total ?? 0;

        $targets = [
            'prepaid' => $targetSum(fn($q) => $q->whereIn('category', ['Prepaid', 'Control'])),
            // 'control' => $targetSum(fn($q) => $q->where('category', 'Control')->whereNotIn('package', ['Postpaid', 'ADSL', 'MNP_IN', 'MNP_OUT'])),
            // 'data' => $targetSum(fn($q) => $q->where('category', 'Data')),
            'postpaid' => $targetSum(fn($q) => $q->where('category', 'Postpaid')),
            'adsl' => $targetSum(fn($q) => $q->where('category', 'ADSL')),
            'wallet' => $targetSum(fn($q) => $q->where('category', 'Wallet')),
        ];



        // $targets['prepaid'] = max(1, round($targets['prepaid'] / $starget));
        // $targets['control'] = max(1, round($targets['control'] / $starget));


        $actuals = [
            'prepaid' => $countSum('mobiles', fn($q) => $q->whereIn('contract_type', ['Control', 'Prepaid'])
                ->whereNotIn('addons', ['MNP_IN', 'MNP_OUT'])->orWhereNull('addons')),
            // 'data' => $countSum('mobiles', fn($q) => $q->where('contract_type', 'Prepaid')
            // ->whereNotIn('addons', ['MNP_IN', 'MNP_OUT'])->orWhereNull('addons')->where('product_name', 'Data SIM')),
            'postpaid' => $countSum('mobiles', fn($q) => $q->where('contract_type', 'Postpaid')
                ->whereNotIn('addons', ['MNP_IN', 'MNP_OUT'])->orWhereNull('addons')),
            'adsl' => $countSum('adsls', fn($q) => $q->where('contract_type', 'New ADSL')),
            'wallet' => $countSum('mobiles', fn($q) => $q->where('contract_type', 'Wallet')),
        ];

        $percentages = [];
        foreach ($targets as $key => $val) {




            $required = max(1, round($val / $starget));



            $achieved = $actuals[$key];




            $percentages[$key] = [
                'requiredtar' => $required,
                'achievedtar' => $achieved,
                'percentage' => round(($achieved / $required) * 100, 1),
                'display' => round(($achieved / $required) * 100)
            ];
        }



        $today = now()->day;
        $lastDay = now()->daysInMonth;
        $packages = DB::table('mobile_packages')->whereNotNull('num')->orderBy('num', 'ASC')->get();


        $results = [];

        foreach ($packages as $pkg) {
            $row = ['package' => $pkg->package_name];

            // MOBILE category
            $target = DB::table('target_report')->whereBetween('date', [$startDate, $endDate])
                ->where('package', $pkg->package_name)
                ->where('type', 'Agent_Target')
                ->where('quantity', '>', 0)
                ->where('location', $location)
                ->first();
            if ($target) {
                $requiredTarget = ceil($target->quantity / $starget);

                $achieved = DB::table('mobiles')
                    ->whereBetween('cashbox_date', [$startDate, $endDate])
                    ->where(function ($q) use ($pkg) {
                        $q->where('package_code', $pkg->id)
                            ->orWhere('addons', $pkg->id);
                    })

                    ->count();

                $projection = ($requiredTarget > 0 && $today > 0)
                    ? round(($achieved / $today * $lastDay) / $requiredTarget * 100, 1) . '%'
                    : '0%';

                $row += [
                    'target' => $requiredTarget,
                    'achieved' => $achieved,
                    'required' => $achieved - $requiredTarget,
                    'projection' => $projection,
                ];
            }

            // Repeat similar blocks for ADSL, Fixed (landline), Device using adjusted tables/logic

            $results[] = $row;
        }






        return view('reports.target', [
            'location' => $location,
            'date' => Carbon::now()->format('F, Y'),
            'percentages' => $percentages,
            'results' => $results,

        ]);
    }



    public function ShowLocationReport(Request $request)
    {


        $user = Auth::user();

        $datos = $user->id;
        $location = $user->location_id;
        $title = $user->title;
        $rank = $user->rank;



        $starget = match ($rank) {
            'A', null => 1,
            'B' => 2,
            'C' => 3,
            'D' => 4,
            default => 1,
        };

        // $location = $request->input('location', $location);
        $startDate = Carbon::now()->startOfMonth()->toDateString();
        $endDate = Carbon::now()->endOfMonth()->toDateString();

        // Helper query function
        $targetSum = fn($conditions) =>
        DB::table('target_report')->selectRaw('SUM(quantity) as total')
            ->whereBetween('date', [$startDate, $endDate])
            ->where('type', 'Location_Target')
            ->where('quantity', '>', 0)

            ->where('location', $location)
            ->where($conditions)
            ->first()->total ?? 0;


        $countSum = fn($table, $conditions) =>
        DB::table($table)->selectRaw('COUNT(*) as total')
            ->whereBetween('cashbox_date', [$startDate, $endDate])
            ->where('location_id', $location)
            ->where($conditions)
            ->first()->total ?? 0;

        $targets = [
            'prepaid' => $targetSum(fn($q) => $q->whereIn('category', ['Prepaid', 'Control'])),
            // 'control' => $targetSum(fn($q) => $q->where('category', 'Control')->whereNotIn('package', ['Postpaid', 'ADSL', 'MNP_IN', 'MNP_OUT'])),
            // 'data' => $targetSum(fn($q) => $q->where('category', 'Data')),
            'postpaid' => $targetSum(fn($q) => $q->where('category', 'Postpaid')),
            'adsl' => $targetSum(fn($q) => $q->where('category', 'ADSL')),
            'wallet' => $targetSum(fn($q) => $q->where('category', 'Wallet')),
        ];



        // $targets['prepaid'] = max(1, round($targets['prepaid'] / $starget));
        // $targets['control'] = max(1, round($targets['control'] / $starget));


        $actuals = [
            'prepaid' => $countSum('mobiles', fn($q) => $q->whereIn('contract_type', ['Control', 'Prepaid'])),
            // 'data' => $countSum('mobiles', fn($q) => $q->where('contract_type', 'Prepaid')
            // ->whereNotIn('addons', ['MNP_IN', 'MNP_OUT'])->orWhereNull('addons')->where('product_name', 'Data SIM')),
            'postpaid' => $countSum('mobiles', fn($q) => $q->where('contract_type', 'Postpaid')),
            'adsl' => $countSum('adsls', fn($q) => $q->where('contract_type', 'New ADSL')),
            'wallet' => $countSum('mobiles', fn($q) => $q->where('contract_type', 'Wallet')),
        ];

        $percentages = [];
        foreach ($targets as $key => $val) {




            $required = max(1, round($val / $starget));



            $achieved = $actuals[$key];




            $percentages[$key] = [
                'requiredtar' => $required,
                'achievedtar' => $achieved,
                'percentage' => round(($achieved / $required) * 100, 1),
                'display' => round(($achieved / $required) * 100)
            ];
        }



        $today = now()->day;
        $lastDay = now()->daysInMonth;
        $packages = DB::table('mobile_packages')->whereNotNull('num')->orderBy('num', 'ASC')->get();



        // $results = [];

        // foreach ($packages as $pkg) {
        //     $row = ['package' => $pkg->package_name];

        //     // MOBILE category
        //     $target = DB::table('target_report')->whereBetween('date', [$startDate, $endDate])
        //         ->where('package', $pkg->package_name)
        //         ->where('type', 'Location_Target')
        //         ->where('location', $location)
        //         ->where('quantity', '>', 0)
        //         ->first();
        //     if ($target) {
        //         $requiredTarget = ceil($target->quantity / $starget);

        //         $achieved = DB::table('mobiles')
        //             ->whereBetween('cashbox_date', [$startDate, $endDate])
        //             ->where(function ($q) use ($pkg) {
        //                 $q->where('package_code', $pkg->id)
        //                   ->orWhere('addons', $pkg->id);
        //             })

        //             ->count();

        //         $projection = ($requiredTarget > 0 && $today > 0)
        //             ? round(($achieved / $today * $lastDay) / $requiredTarget * 100, 1) . '%'
        //             : '0%';

        //         $row += [
        //             'target' => $requiredTarget,
        //             'achieved' => $achieved,
        //             'required' => $achieved - $requiredTarget,
        //             'projection' => $projection,
        //         ];
        //     }

        //     // Repeat similar blocks for ADSL, Fixed (landline), Device using adjusted tables/logic

        //     $results[] = $row;
        // }


        $results = [];

        foreach ($packages as $pkg) {
            $row = ['package' => $pkg->package_name];

            // ======= 1. Get the Target =======
            $target = DB::table('target_report')
                ->whereBetween('date', [$startDate, $endDate])
                ->where('package', $pkg->package_name)
                ->where('type', 'Location_Target')
                ->where('location', $location)
                ->where('quantity', '>', 0)
                ->first();

            if ($target) {
                $requiredTarget = ceil($target->quantity / $starget);

                // ======= 2. Mobile Achieved =======
                $mobileAchieved = DB::table('mobiles')
                    ->whereBetween('cashbox_date', [$startDate, $endDate])
                    ->whereIn('contract_type', ['Control', 'Prepaid', 'Postpaid', 'Wallet'])
                    ->where(function ($q) use ($pkg) {
                        $q->where('package_code', $pkg->id)
                            ->orWhereRaw('FIND_IN_SET(?, addons)', [$pkg->id]);
                    })
                    ->count();

                // ======= 3. ADSL Achieved =======
                $adslAchieved = DB::table('adsls')
                    ->whereBetween('cashbox_date', [$startDate, $endDate])
                    ->where('package_code', $pkg->id)
                    ->count();

                // ======= 4. Landline (Fixed) Achieved =======
                $landlineAchieved = DB::table('landlines')
                    ->whereBetween('cashbox_date', [$startDate, $endDate])
                    ->where('package_code', $pkg->id)
                    ->count();

                // ======= 5. Total Achieved =======
                $totalAchieved = $mobileAchieved + $adslAchieved + $landlineAchieved;

                // ======= 6. Projection Calculation =======
                $projection = ($requiredTarget > 0 && $today > 0)
                    ? round(($totalAchieved / $today * $lastDay) / $requiredTarget * 100, 1) . '%'
                    : '0%';

                // ======= 7. Add to Row =======
                $row += [
                    'target' => $requiredTarget,
                    'achieved' => $totalAchieved,
                    'required' => $totalAchieved - $requiredTarget,
                    'projection' => $projection,
                ];
            } else {
                $row += [
                    'target' => 0,
                    'achieved' => 0,
                    'required' => 0,
                    'projection' => '0%',
                ];
            }

            $results[] = $row;
        }


        return view('reports.target_location', [
            'location' => $location,
            'date' => Carbon::now()->format('F, Y'),
            'percentages' => $percentages,
            'results' => $results,

        ]);
    }





    public function AgentRank(Request $request)
    {
    $currentUser = Auth::user();

    // Get location from query or fallback to current user's location
    $location = $request->query('location_id') ?? $currentUser->location_id;

    // Start date handling
    if (!empty($request->query('datefrom'))) {
        $startDate = Carbon::createFromFormat('Y-m', $request->query('datefrom'))->startOfMonth()->toDateString();
        $startmonth = $request->query('datefrom');
    } else {
        $startDate = Carbon::now()->startOfMonth()->toDateString();
        $startmonth = Carbon::now()->format('Y-m');
    }

    // End date handling
    if (!empty($request->query('dateto'))) {
        $endDate = Carbon::createFromFormat('Y-m', $request->query('dateto'))->endOfMonth()->toDateString();
        $endtmonth = $request->query('dateto');
    } else {
        $endDate = Carbon::now()->endOfMonth()->toDateString();
        $endtmonth = Carbon::now()->format('Y-m');
    }

    // Sales from mobiles
    $mobiles = DB::table('mobiles')
        ->select(
            'user_id',
            DB::raw('COUNT(*) as mobile_sales'),
            DB::raw('0 as adsl_sales'),
            DB::raw('0 as landline_sales')
        )
        ->whereBetween('cashbox_date', [$startDate, $endDate])
        ->where('location_id', $location)
        ->whereIn('contract_type', ['Prepaid', 'Control', 'Postpaid'])
        ->groupBy('user_id');

    // Sales from adsls
    $adsls = DB::table('adsls')
        ->select(
            'user_id',
            DB::raw('0 as mobile_sales'),
            DB::raw('COUNT(*) as adsl_sales'),
            DB::raw('0 as landline_sales')
        )
        ->whereBetween('cashbox_date', [$startDate, $endDate])
        ->where('location_id', $location)
        ->whereIn('contract_type', ['New ADSL'])
        ->groupBy('user_id');

    // Sales from landlines
    $landlines = DB::table('landlines')
        ->select(
            'user_id',
            DB::raw('0 as mobile_sales'),
            DB::raw('0 as adsl_sales'),
            DB::raw('COUNT(*) as landline_sales')
        )
        ->whereBetween('cashbox_date', [$startDate, $endDate])
        ->where('location_id', $location)
        ->whereIn('contract_type', ['New Landline'])
        ->groupBy('user_id');

    // Merge all sales
    $combined = $mobiles
        ->unionAll($adsls)
        ->unionAll($landlines);

    // Aggregate and sort
    $sales = DB::table(DB::raw("({$combined->toSql()}) as combined"))
        ->mergeBindings($combined)
        ->select(
            'user_id',
            DB::raw('SUM(mobile_sales) as mobile_sales'),
            DB::raw('SUM(adsl_sales) as adsl_sales'),
            DB::raw('SUM(landline_sales) as landline_sales'),
            DB::raw('SUM(mobile_sales + adsl_sales + landline_sales) as total_sales')
        )
        ->groupBy('user_id')
        ->orderByDesc('total_sales')
        ->get();

    // Attach usernames
    $sales = $sales->map(function ($item) {
        $user = User::find($item->user_id);
        $item->username = $user ? $user->username : 'Unknown';
        $item->name = $user ? $user->name : 'Unknown';
        return $item;
    });

        $locations     = locations::all();
        $locationname = locations::where('id',$location)->value('name_en');

$date = Carbon::now()->format('F, Y');

    return view('target.agentrank', compact('sales', 'startDate', 'endDate', 'location', 'locations', 'startmonth', 'endtmonth', 'locationname', 'date'));

    }
        
}
