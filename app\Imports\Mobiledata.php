<?php

namespace App\Imports;

use App\Models\User;
use App\Models\cashbox;
use App\Models\adsl;
use App\Models\devices;
use App\Models\mobile;
use App\Models\landline;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;



class Mobiledata implements ToModel
{
    /**
     * @param array $row
     *
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public $totalVisa = 0;
    public $totalCash = 0;
    public $totalCheque = 0;
    public $totaltax = 0;
    public $totalbank = 0;
    public $totalvat = 0;
    public $totalCountmobile = 0;

    public $totalCountprepaid = 0;
    public $totalCountpostpaid = 0;
    public $totalCountdata = 0;

    public $totalCountadsl = 0;
    public $totalCountlandline = 0;
    public $totalCountdevice = 0;
    public $totalCountreceipt = 0;
    public $totalCounttransaction = 0;
    public $totalcashbox = 0;


    public function model(array $row)   
    {

        $user = Auth::user();


$cashboxdate1 = trim($row[0]);
$sku = trim($row[1]);
$snumber = trim($row[2]);
$contracttype = trim($row[5]);
$username = trim($row[6]);

$package_name = trim($row[3]);

$formatted  = (int) trim($row[0]); // cast to integer




$package_code1 = 1;
$productcode = 1;
if($contracttype == 'Wallet'){$productcode = 5 ; $package_code1 = 3;}else{
    
    
    $productcode = 1; 
    
    if($contracttype == 'Control' || $contracttype == 'Prepaid' ){$package_code1 = 1;} 
    if($contracttype == 'Postpaid'){$package_code1 = 8;} 


}
   
        // Format as Y-m-d
        // $formatted = $convertedDate->format('Y-m-d');


        // Check if the number already exists in the database
        if ($username ) {

            // $exists = mobile::where('number', $snumber)
            //     ->exists();

        $useriddata = User::where('username',$username)->value('id');

         if($useriddata){


                mobile::create([
                    'cashbox_date'  => $cashboxdate1,
                    'product_sku'   => $sku,
                    'number'        => $snumber,
                    'contract_type' => $contracttype,
                    'product_code'  => $productcode,
                    'package_code'  => $package_code1,
                    'product_name'  => $package_name,
                    'location_id'   => 1,
                    'user_id' => $useriddata,


                ]);

            }
        }


    }
}
