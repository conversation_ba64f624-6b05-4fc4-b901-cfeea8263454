<table>
    <thead>
        <tr>
            <th>ID</th>
            <th>Mobile Number</th>
            <th>Contract Type</th>
            <th>Package</th>
            <th>Username</th>
            <th>Cashbox Date</th>
            <th>Created Date</th>
            <th>Location</th>
        </tr>
    </thead>
    <tbody>
        @foreach($mobiles as $mobile)
            <tr>
                <td>{{ $mobile->id }}</td>
                <td>{{ $mobile->number }}</td>
                <td>{{ $mobile->contract_type }}</td>
                <td>{{ $mobile->packages->package_name ?? 'N/A' }}</td>
                <td>{{ $mobile->users->nick_name ?? $mobile->users->username ?? 'N/A' }}</td>
                <td>{{ $mobile->cashbox_date ?? 'N/A' }}</td>
                <td>{{ $mobile->created_at }}</td>
                <td>{{ $mobile->location->name_en ?? $mobile->location_id }}</td>
            </tr>
        @endforeach
    </tbody>
</table>