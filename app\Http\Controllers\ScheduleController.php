<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\locations;
use App\Models\Schedule;
use App\Models\logs;
use App\Models\UserPlan;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Carbon\Carbon;
use Illuminate\Support\Facades\Schema;

class ScheduleController extends Controller
{


    public function index(Request $request)
    {

        $user = Auth::user();
        $location = $user->location_id;

        $rotationFilter = $request->input('rotation'); // e.g. 'Rotation_1'

        $rotationMap = [
            'Rotation_1' => ['Rotation1', 'Regular'],
            'Rotation_2' => ['Rotation2', 'Regular'],
            'Rotation_3' => ['Rotation3', 'Regular'],
            'Rotation_4' => ['Rotation4', 'Regular'],
        ];

        $rotations = $rotationMap[$rotationFilter] ?? ['Rotation1', 'Rotation2', 'Rotation3', 'Rotation4', 'Regular'];

        $currentMonth = $request->input('month');
        $years        = $request->input('year');


        $employees = User::where('location_id', $location)
            ->whereIn('rotation', $rotations)
            ->orderBy('rotation')
            ->orderBy('located_at')
            ->limit(30)
            ->get();

        $selectedDate  = UserPlan::find(2)?->date;
        $years  = UserPlan::find(2)?->year;
        $currentMonth = UserPlan::find(2)?->month;
        $locations     = locations::all();

        $schedules = Schedule::when($selectedDate, function ($query, $date) {
            return $query->where('date', 'like', $date . '%');
        })->get();

        $attendanceData = logs::whereIn('date', $schedules->pluck('date'))
            ->get()
            ->groupBy(fn($att) => $att->username . '_' . $att->date);




        return view('schedule.scheduleview', compact('employees', 'schedules', 'attendanceData', 'locations', 'years', 'location', 'currentMonth', 'rotations'));
    }






    public function FullTable(Request $request)
    {

        $user = Auth::user();
        $location = $user->location_id;

        $rotationFilter = $request->input('rotation'); // e.g. 'Rotation_1'

        $rotationMap = [
            'Rotation_1' => ['Rotation1', 'Regular'],
            'Rotation_2' => ['Rotation2', 'Regular'],
            'Rotation_3' => ['Rotation3', 'Regular'],
            'Rotation_4' => ['Rotation4', 'Regular'],
        ];

        $rotations = $rotationMap[$rotationFilter] ?? ['Rotation1', 'Rotation2', 'Rotation3', 'Rotation4', 'Regular'];

        $currentMonth = $request->input('month');
        $years        = $request->input('year');


        $employees = User::where('location_id', $location)
            ->whereIn('rotation', $rotations)
            ->orderBy('rotation')
            ->orderBy('located_at')
            ->limit(30)
            ->get();

        $selectedDate  = UserPlan::find(2)?->date;
        $years  = UserPlan::find(2)?->year;
        $currentMonth = UserPlan::find(2)?->month;
        $locations     = locations::all();

        $schedules = Schedule::when($selectedDate, function ($query, $date) {
            return $query->where('date', 'like', $date . '%');
        })->get();

        $attendanceData = logs::whereIn('date', $schedules->pluck('date'))
            ->get()
            ->groupBy(fn($att) => $att->username . '_' . $att->date);




        return view('schedule.fulltable', compact('employees', 'schedules', 'attendanceData', 'locations', 'years', 'location', 'currentMonth', 'rotations'));
    }





    public function filter(Request $request)
    {
        $location = $request->input('location');
        $rotationFilter = $request->input('rotation'); // e.g. 'Rotation_1'

        $rotationMap = [
            'Rotation1' => ['Rotation1', 'Regular'],
            'Rotation2' => ['Rotation2', 'Regular'],
            'Rotation3' => ['Rotation3', 'Regular'],
            'Rotation4' => ['Rotation4', 'Regular'],
        ];

        // Default all rotations if not found
        $rotations = $rotationMap[$rotationFilter] ?? array_merge(...array_values($rotationMap));




        $currentMonth = $request->input('month');
        $years = $request->input('year');

        // Build query
        $query = User::where('location_id', $location)->whereNotNull('rotation')->where('rotation', '!=', '');

        if (!empty($rotationFilter)) {
            $query->whereIn('rotation', $rotations);
        }

        $employees = $query->orderBy('rotation')
            ->orderBy('located_at')
            ->limit(30)
            ->get();

        $daterange =  $request->input('year') . '-' . $request->input('month');

        $userPlan = UserPlan::find(2);

        if ($userPlan) {
            $userPlan->year = $request->input('year');
            $userPlan->month = $request->input('month');
            $userPlan->date = $daterange;
            $userPlan->save();
        }

        $selectedDate  = UserPlan::find(2)?->date;
        $years         = UserPlan::find(2)?->year;
        $currentMonth  = UserPlan::find(2)?->month;
        $locations     = locations::all();

        $schedules = Schedule::when($selectedDate, function ($query, $date) {
            return $query->where('date', 'like', $date . '%');
        })->get();

        $attendanceData = logs::whereIn('date', $schedules->pluck('date'))
            ->get()
            ->groupBy(fn($att) => $att->username . '_' . $att->date);





        return view('schedule.scheduleview', compact('employees', 'schedules', 'attendanceData', 'locations', 'years', 'location', 'currentMonth', 'rotations', 'rotationFilter'));
    }







    public function StoreAnnualPlan(Request $request)
    {


        $request->validate([
            'rowid' => ['required', 'max:255'],
            'username' => 'required',

        ]);



        $rownumber  = $request->rowid;
        $username   = $request->username;
        $datestart  = $request->dateuser;
        $shift      = $request->shift;

        $annnum    = $request->annual_num;
        $add = str_replace(".", "_", $username);

        $today = date('Y-m-d');

        $annualto = date('Y-m-d', (strtotime('-1 day', strtotime($datestart))));

        $datefrom =  date("Y-01-01", strtotime($datestart));


        if ($request->annual_plan) {

            if (substr($request->annual_plan, -5) === 'Leave') {

                $rows = DB::table('sched')
                    ->whereBetween('date', [$datefrom, $annualto])
                    ->whereRaw("SUBSTR($add, -12) = ?", [$request->annual_plan])
                    ->get();

                $parts2 = 0;

                foreach ($rows as $row) {
                    $num33 = $row->$add;
                    $parts = explode('|', $num33);

                    if (isset($parts[1])) {
                        $parts2 +=  $parts[1];
                    }
                }


                $num1 = DB::table('sched')
                    ->whereBetween('date', [$datefrom, $annualto])
                    ->whereRaw("SUBSTR($add, -12) = ?", [$request->annual_plan])
                    ->count();

                $num1 = $num1 + 1;

                $f_stamp1 = $parts2 +  $annnum;



                $newcomm = $num1 . '|' . $annnum . '|' . $f_stamp1 . '|' . $request->annual_plan;

                DB::table('sched')->where('id', $rownumber)->update([$add => $newcomm]);
            } else {


                if ($request->annual_plan === 'Permission') {
                }

                if ($request->annual_plan === 'Instead_OF' || $request->annual_plan === 'Training') {


                    $newcomm = $request->annual_plan . ' | ' . $request->comment;


                    DB::table('sched')->where('id', $rownumber)->update([$add => $newcomm]);
                }
            }
        } else {


            if ($request->shift == 'Clear') {


                DB::table('sched')->where('id', $rownumber)->update([$add => null]);
            } else {

                DB::table('sched')->where('id', $rownumber)->update([$add => $shift]);
            }
        }

        return redirect('/schedule');
    }



    public function AnnualReport()
    {

        $locations     = locations::all();
        $user = Auth::user();
        $location = $user->location_id;

        $to = date('Y-m-d');
        $from =  date("Y-m-01", strtotime($to));



        $users = DB::table('users')
            ->where('location_id', $location)
            ->whereNotIn('username', ['ramyshalaby', 'guest'])
            ->whereIn('rotation', ['Rotation1', 'Rotation2', 'Rotation3', 'Rotation4', 'Regular'])
            ->orderBy('rotation')
            ->get();

        $reportData = [];

        foreach ($users as $user) {
            $column = str_replace('.', '_', $user->username);

            // Handle annual leave parsing separately
            $annualLeave = 0;
            $rows = DB::table('sched')
                ->select($column)
                ->whereBetween('date', [$from, $to])
                ->whereRaw("SUBSTR(`$column`, -12) = 'Annual_Leave'")
                ->get();

            $parts = 0;

            foreach ($rows as $row) {
                $val = $row->$column;
                if (strpos($val, '|') !== false) {
                    $parts = explode('|', $val);
                    $annualLeave += isset($parts[1]) ? $parts[1] : 0;
                }
            }



            // Count for other leave types
            $leaveCounts = [
                'sick_leave' => DB::table('sched')
                    ->whereBetween('date', [$from, $to])
                    ->where($column, 'Sick_Leave')->count(),

                'paternity_leave' => DB::table('sched')
                    ->whereBetween('date', [$from, $to])
                    ->where($column, 'Paternity_Leave')->count(),

                'over_time' => DB::table('sched')
                    ->whereBetween('date', [$from, $to])
                    ->where($column, 'like', '%Over_Time')->count(),

                'permission' => DB::table('sched')
                    ->whereBetween('date', [$from, $to])
                    ->where($column, 'like', '%Permission')->count(),

                'emergency_leave' => DB::table('sched')
                    ->whereBetween('date', [$from, $to])
                    ->whereRaw("SUBSTR(`$column`, -15) = 'Emergency_Leave'")
                    ->count(),
            ];

            $reportData[] = [
                'nick' => $user->nick_name,
                'user' => $user->username,
                'emp_id' => $user->emp_num,
                'rotation' => $user->rotation,
                'annual_leave' => $annualLeave,
                'emergency_leave' => $leaveCounts['emergency_leave'],
                'sick_leave' => $leaveCounts['sick_leave'],
                'paternity_leave' => $leaveCounts['paternity_leave'],
                'over_time' => $leaveCounts['over_time'],
                'permission' => $leaveCounts['permission'],
            ];
        }



        $date = Carbon::now()->format('F, Y');



        return view(
            'schedule/userannual',
            compact('reportData', 'locations', 'date', 'from', 'to', 'location')

        );
    }


    public function AnnualDetails()
    {

        $locations     = locations::all();
        $user = Auth::user();
        $selecteduser = $user->id;

        $to = date('Y-m-d');
        // $from =  date("Y-m-01", strtotime($to));
        $from = 'Y-01-01';
        $date = Carbon::now()->format('F, Y');
        $column = str_replace('.', '_', $user->username);
        // Handle annual leave parsing separately
        $annualLeave = 0;

        $usersdata = DB::table('users')
            ->whereNotIn('username', ['ramyshalaby', 'guest', 'admin', 'administrator'])
            ->whereIn('rotation', ['Rotation1', 'Rotation2', 'Rotation3', 'Rotation4', 'Regular'])
            ->orderBy('rotation')
            ->get();


        if (!Schema::hasColumn('sched', $column)) {





            // Column doesn't exist, return view with a message or empty data
            return view('schedule.annualdetails', [
                'rows' => collect(), // empty collection
                'usersdata' => $usersdata,
                'date' => Carbon::now()->format('F, Y'),
                'from' => $from,
                'to' => $to,
                'selecteduser' => $selecteduser,
                'sickLeaveCount' => 0,
                'annualLeaveCount' => 0,
                'emergencyLeaveCount' => 0,
                'column' => $column,
                'error' => "Column '$column' does not exist."
            ]);
        } else {

            $rows = DB::table('sched')
                ->select('date', DB::raw("DAYNAME(date) as day"), $column)
                ->whereBetween('date', [$from, $to])
                ->whereRaw("SUBSTR(`$column`, -5) = 'Leave'")
                ->orderBy('date')
                ->get();

            $parts = 0;

            $sickLeaveCount = DB::table('sched')
                ->whereBetween('date', [$from, $to])
                ->where($column, 'like', '%Sick_Leave')
                ->count();

            // Count annual leave
            $annualLeaveCount = DB::table('sched')
                ->whereBetween('date', [$from, $to])
                ->where($column, 'like', '%Annual_Leave')
                ->count();

            // Count annual leave
            $emergencyLeaveCount = DB::table('sched')
                ->whereBetween('date', [$from, $to])
                ->where($column, 'like', '%Emergency_Leave')
                ->count();


            return view(
                'schedule/annualdetails',
                compact('rows', 'usersdata', 'date', 'from', 'to', 'selecteduser', 'column', 'sickLeaveCount', 'annualLeaveCount', 'emergencyLeaveCount')

            );
        }
    }


    public function AnnualDetailsView(Request $request)
    {
        $selecteduser = $request->cashieruser;
        $from = $request->from;
        $to = $request->to;
        $locations     = locations::all();
        $date = Carbon::now()->format('F, Y');


        if (!$selecteduser) {
            $selecteduser = Auth::user()->id;
        }

        $uservalue = User::where('id', $selecteduser)->value('username');



        $usersdata = DB::table('users')
            ->whereNotIn('username', ['ramyshalaby', 'guest', 'admin', 'administrator'])
            ->whereIn('rotation', ['Rotation1', 'Rotation2', 'Rotation3', 'Rotation4', 'Regular'])
            ->orderBy('rotation')
            ->get();

        $date = Carbon::now()->format('F, Y');
        $column = str_replace('.', '_', $uservalue);
        // Handle annual leave parsing separately
        $annualLeave = 0;

        if (!Schema::hasColumn('sched', $column)) {



            // Column doesn't exist, return view with a message or empty data
            return view('schedule.annualdetails', [
                'rows' => collect(), // empty collection
                'usersdata' => $usersdata,
                'date' => Carbon::now()->format('F, Y'),
                'from' => $from,
                'to' => $to,
                'selecteduser' => $selecteduser,
                'sickLeaveCount' => 0,
                'annualLeaveCount' => 0,
                'emergencyLeaveCount' => 0,


                'column' => $column,
                'error' => "Column '$column' does not exist."
            ]);
        } else {

            $annualLeaveCount = 0;

            $rows = DB::table('sched')
                ->select('date', DB::raw("DAYNAME(date) as day"), $column)
                ->whereBetween('date', [$from, $to])
                ->whereRaw("SUBSTR(`$column`, -5) = 'Leave'")
                ->orderBy('date')
                ->get();

            $parts = 0;
            $sickLeaveCount = DB::table('sched')
                ->whereBetween('date', [$from, $to])
                ->where($column, 'like', '%Sick_Leave')
                ->count();

            // Count annual leave
            $annuals = DB::table('sched')
                ->whereBetween('date', [$from, $to])
                ->where($column, 'like', '%Annual_Leave')
                ->get();



            foreach ($annuals as $row) {
                $val = $row->$column;
                if (strpos($val, '|') !== false) {
                    $parts = explode('|', $val);
                    $annualLeaveCount += isset($parts[1]) ? $parts[1] : 0;
                }
            }



            // Count annual leave
            $emergencyLeaveCount = DB::table('sched')
                ->whereBetween('date', [$from, $to])
                ->where($column, 'like', '%Emergency_Leave')
                ->count();

            return view(
                'schedule/annualdetails',
                compact('rows', 'usersdata', 'date', 'from', 'to', 'selecteduser', 'column', 'sickLeaveCount', 'annualLeaveCount', 'emergencyLeaveCount')

            );
        }
    }

    public function AnnualView(Request $request)
    {
        $location = $request->location;
        $from = $request->from;
        $to = $request->to;
        $locations     = locations::all();
        $date = Carbon::now()->format('F, Y');

        $users = DB::table('users')
            ->where('location_id', $location)
            ->whereNotIn('username', ['ramyshalaby', 'guest'])
            ->whereIn('rotation', ['Rotation1', 'Rotation2', 'Rotation3', 'Rotation4', 'Regular'])
            ->orderBy('rotation')
            ->get();

        $reportData = [];

        foreach ($users as $user) {
            $column = str_replace('.', '_', $user->username);

            // Handle annual leave parsing separately
            $annualLeave = 0;
            $rows = DB::table('sched')
                ->select($column)
                ->whereBetween('date', [$from, $to])
                ->whereRaw("SUBSTR(`$column`, -12) = 'Annual_Leave'")
                ->get();

            foreach ($rows as $row) {
                $val = $row->$column;
                if (strpos($val, '|') !== false) {
                    $parts = explode('|', $val);
                    $annualLeave += isset($parts[1]) ? $parts[1] : 0;
                }
            }

            // Count for other leave types
            $leaveCounts = [
                'sick_leave' => DB::table('sched')
                    ->whereBetween('date', [$from, $to])
                    ->where($column, 'Sick_Leave')->count(),

                'paternity_leave' => DB::table('sched')
                    ->whereBetween('date', [$from, $to])
                    ->where($column, 'Paternity_Leave')->count(),

                'over_time' => DB::table('sched')
                    ->whereBetween('date', [$from, $to])
                    ->where($column, 'like', '%Over_Time')->count(),

                'permission' => DB::table('sched')
                    ->whereBetween('date', [$from, $to])
                    ->where($column, 'like', '%Permission')->count(),

                'emergency_leave' => DB::table('sched')
                    ->whereBetween('date', [$from, $to])
                    ->whereRaw("SUBSTR(`$column`, -15) = 'Emergency_Leave'")
                    ->count(),
            ];

            $reportData[] = [
                'nick' => $user->nick_name,
                'user' => $user->username,
                'emp_id' => $user->emp_num,
                'rotation' => $user->rotation,
                'annual_leave' => $annualLeave,
                'emergency_leave' => $leaveCounts['emergency_leave'],
                'sick_leave' => $leaveCounts['sick_leave'],
                'paternity_leave' => $leaveCounts['paternity_leave'],
                'over_time' => $leaveCounts['over_time'],
                'permission' => $leaveCounts['permission'],
            ];
        }

        return view(
            'schedule.userannual',
            compact('reportData', 'locations', 'from', 'to', 'date', 'location')
        );
    }
}
