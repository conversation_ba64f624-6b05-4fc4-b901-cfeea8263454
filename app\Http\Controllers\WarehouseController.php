<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\warehouse;
use App\Models\stocktransactions;
class WarehouseController extends Controller
{
    public function WareHouseView()
    {
        $warehouses = warehouse::orderBy('created_at', 'desc')->paginate(10);


        return view('warehouse/warehouseview', ['warehouses' => $warehouses, 'searchcat' => '']);
    }



    public function StockTransactionView()
    {
        $stockTransactions = stocktransactions::orderBy('created_at', 'desc')->get();

        return view('warehouse/stocktransaction', ['transactions' => $stockTransactions, 'searchcat' => '']);
    }







}
