<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;
use App\Services\ReportService;
use App\Models\mobile;
use App\Models\User;
use App\Models\locations;

class ReportsController extends Controller
{


    public function ShowUsersTarget(Request $request)
    {
        // $currentUser = Auth::user();
        // $location = $currentUser->location_id;

        // $startDate = Carbon::now()->startOfMonth()->toDateString();
        // $endDate = Carbon::now()->endOfMonth()->toDateString();
        $today = now()->day;
        $lastDay = now()->daysInMonth;


        $currentUser = Auth::user();

        // $startDate = $request->query('datefrom') ??  Carbon::now()->startOfMonth()->toDateString();
        // $endDate = $request->query('dateto') ??  Carbon::now()->endOfMonth()->toDateString();

        $location = !empty($request->query('location_id')) ? $request->query('location_id') : $currentUser->location_id;


        if (!empty($request->query('datefrom'))) {

            $startDate = Carbon::createFromFormat('Y-m', $request->query('datefrom'))->startOfMonth()->toDateString();

            $startmonth = $request->query('datefrom');
        } else {

            $startDate = Carbon::now()->startOfMonth()->toDateString();

            $startmonth = request('month', \Carbon\Carbon::now()->format('Y-m'));
        }


        if (!empty($request->query('dateto'))) {

            $endDate = Carbon::createFromFormat('Y-m', $request->query('dateto'))->endOfMonth()->toDateString();
            $endtmonth = $request->query('dateto');
        } else {

            $endDate = Carbon::now()->endOfMonth()->toDateString();
            $endtmonth = request('month', \Carbon\Carbon::now()->format('Y-m'));
        }




        $users = User::where('location_id', $location)->whereNotNull('rank')
            ->where('rank', '!=', '')->get();
        $packages = DB::table('mobile_packages')
            ->whereNotNull('num')
            ->orderBy('num', 'ASC')
            ->get();

        $results = [];

        foreach ($users as $user) {
            $rank = $user->rank;

            $starget = match ($rank) {
                'A', null => 1,
                'B' => 2,
                'C' => 3,
                'D' => 4,
                default => 1,
            };

            $userData = [
                'name' => $user->nick_name ?? $user->username,
                'rank' => $rank,
                'packages' => [],
            ];

            foreach ($packages as $pkg) {
                $target = DB::table('target_report')
                    ->whereBetween('date', [$startDate, $endDate])
                    ->where('package', $pkg->package_name)
                    ->where('type', 'Agent_Target')
                    ->where('quantity', '>', 0)
                    ->where('location', $location)
                    ->first();

                if ($target) {
                    $requiredTarget = ceil($target->quantity / $starget);

                    $mobileAchieved = DB::table('mobiles')
                        ->whereBetween('cashbox_date', [$startDate, $endDate])
                        ->where('user_id', $user->id)
                        ->where(function ($q) use ($pkg) {
                            $q->where('package_code', $pkg->id)
                                ->orWhere('addons', $pkg->id);
                        })
                        ->count();

                    $adslAchieved = DB::table('adsls')
                        ->whereBetween('cashbox_date', [$startDate, $endDate])
                        ->where('user_id', $user->id)
                        ->where('package_code', $pkg->id)
                        ->count();

                    // Count of New Landline
                    $landlineAchieved = DB::table('landlines')
                        ->whereBetween('cashbox_date', [$startDate, $endDate])
                        ->where('user_id', $user->id)
                        ->where('package_code', $pkg->id)
                        ->count();

                    $achieved = $mobileAchieved + $adslAchieved + $landlineAchieved;

                    $projection = ($requiredTarget > 0 && $today > 0)
                        ? round(($achieved / $today * $lastDay) / $requiredTarget * 100, 1)
                        : 0;

                    $userData['packages'][$pkg->package_name] = [
                        'achieved' => $achieved,
                        'remaining' => max(0, $requiredTarget - $achieved),
                        'projection' => $projection . '%',
                    ];
                }
            }

            $results[] = $userData;
        }
        $monthlyTargets = DB::table('target_report')
            ->where('type', 'Agent_Target')
            ->whereBetween('date', [$startDate, $endDate])
            ->where('location', $location)
            ->where('quantity', '>', 0)
            ->orderBy('package')
            ->get();

        $locations = locations::all();
        $locationname = locations::where('id', $location)->value('name_en');
        $todaydate = date('Y-m-d');


        return view('reports.usertarget', [
            'location' => $location,
            'locationname' => $locationname,
            'locations' => $locations,
            'date' => now()->format('F, Y'),
            'results' => $results,
            'startDate' => $startDate,
            'endDate' => $endDate,
            'startmonth' => $startmonth,
            'endtmonth' => $endtmonth,
            'monthlyTargets' => $monthlyTargets,

        ]);
    }


    public function ShowUsersPerTarget(Request $request)
    {
        $currentUser = Auth::user();
        $location = $request->query('location_id') ?? $currentUser->location_id;

        // Start Date
        if ($request->query('datefrom')) {
            $startDate = Carbon::createFromFormat('Y-m', $request->query('datefrom'))->startOfMonth()->toDateString();
            $startmonth = $request->query('datefrom');
        } else {
            $startDate = Carbon::now()->startOfMonth()->toDateString();
            $startmonth = Carbon::now()->format('Y-m');
        }

        // End Date
        if ($request->query('dateto')) {
            $endDate = Carbon::createFromFormat('Y-m', $request->query('dateto'))->endOfMonth()->toDateString();
            $endtmonth = $request->query('dateto');
        } else {
            $endDate = Carbon::now()->endOfMonth()->toDateString();
            $endtmonth = Carbon::now()->format('Y-m');
        }

        $users = User::where('location_id', $location)
            ->whereNotNull('rank')
            ->where('rank', '!=', '')
            ->get();

        $packages = DB::table('mobile_packages')
            ->whereNotNull('num')
            ->orderBy('num', 'ASC')
            ->get();

        $results = [];

        foreach ($users as $user) {
            $userData = [
                'user_id' => $user->id,
                'name' => $user->username,
                'nickname' => $user->nick_name,
                'rank' => $user->rank ?? 'N/A',
                'packages' => [],
            ];

            $starget = match ($user->rank) {
                'B' => 2,
                'C' => 3,
                'D' => 4,
                default => 1,
            };

            // Per-package achievement
            foreach ($packages as $pkg) {
                $targetRow = DB::table('target_report')
                    ->whereBetween('date', [$startDate, $endDate])
                    ->where('package', $pkg->package_name)
                    ->where('type', 'Agent_Target')
                    ->where('quantity', '>', 0)
                    ->where('location', $location)
                    ->first();

                if ($targetRow) {
                    $requiredTarget = ceil($targetRow->quantity / $starget);

                    $mobileAchieved = DB::table('mobiles')
                        ->whereBetween('cashbox_date', [$startDate, $endDate])
                        ->where('user_id', $user->id)
                        ->where(function ($q) use ($pkg) {
                            $q->where('package_code', $pkg->id)
                                ->orWhere('addons', $pkg->id);
                        })
                        ->count();

                    $adslAchieved = DB::table('adsls')
                        ->whereBetween('cashbox_date', [$startDate, $endDate])
                        ->where('user_id', $user->id)
                        ->where('package_code', $pkg->id)
                        ->count();

                    $landlineAchieved = DB::table('landlines')
                        ->whereBetween('cashbox_date', [$startDate, $endDate])
                        ->where('user_id', $user->id)
                        ->where('package_code', $pkg->id)
                        ->count();

                    $achieved = $mobileAchieved + $adslAchieved + $landlineAchieved;

                    $percentage = $requiredTarget > 0 ? round($achieved / $requiredTarget * 100, 1) : 0;

                    $userData['packages'][$pkg->package_name] = $percentage;
                }
            }

            // Overall achievement across categories
            $totalTarget = 0;
            $totalAchieved = 0;

            $categories = [
                'Mobile' => 'mobiles',
                'ADSL' => 'adsls',
                'Fixed' => 'landlines',
            ];

            foreach ($categories as $category => $table) {
                $targetRow = DB::table('target_report')
                    ->whereBetween('date', [$startDate, $endDate])
                    ->where('type', 'Agent_Target')
                    ->where('category', $category)
                    ->where('location', $location)
                    ->where('quantity', '>', 0)
                    ->first();

                if ($targetRow) {
                    $target = ceil($targetRow->quantity / $starget);
                    $totalTarget += $target;

                    $achieved = DB::table($table)
                        ->whereBetween('cashbox_date', [$startDate, $endDate])
                        ->where('user_id', $user->id)
                        ->count();

                    $totalAchieved += $achieved;
                }
            }

            $overallAchievement = $totalTarget > 0 ? round($totalAchieved / $totalTarget * 100, 1) : 0;
            $userData['overall'] = $overallAchievement;

            $results[] = $userData;
        }

        $allPackages = $packages->pluck('package_name')->unique();

        $monthlyTargets = DB::table('target_report')
            ->where('type', 'Agent_Target')
            ->whereBetween('date', [$startDate, $endDate])
            ->where('location', $location)
            ->where('quantity', '>', 0)
            ->orderBy('package')
            ->get();

        $locations = locations::all();
        $todaydate = date('Y-m-d');
        $locationname = locations::where('id', $location)->value('name_en');

        return view('reports.usertargetper', [
            'location' => $location,
            'locations' => $locations,
            'locationname' => $locationname,
            'startDate' => $startDate,
            'endDate' => $endDate,
            'startmonth' => $startmonth,
            'endtmonth' => $endtmonth,
            'date' => now()->format('F, Y'),
            'results' => $results,
            'allPackages' => $allPackages,
            'monthlyTargets' => $monthlyTargets,
        ]);
    }




    // public function ShowUsersPerTarget(Request $request)
    // {
    //     $currentUser = Auth::user();

    //     $location = !empty($request->query('location_id')) ? $request->query('location_id') : $currentUser->location_id;


    //     if (!empty($request->query('datefrom'))) {

    //         $startDate = Carbon::createFromFormat('Y-m', $request->query('datefrom'))->startOfMonth()->toDateString();

    //         $startmonth = $request->query('datefrom');
    //     } else {

    //         $startDate = Carbon::now()->startOfMonth()->toDateString();

    //         $startmonth = request('month', \Carbon\Carbon::now()->format('Y-m'));
    //     }


    //     if (!empty($request->query('dateto'))) {

    //         $endDate = Carbon::createFromFormat('Y-m', $request->query('dateto'))->endOfMonth()->toDateString();
    //         $endtmonth = $request->query('dateto');
    //     } else {

    //         $endDate = Carbon::now()->endOfMonth()->toDateString();
    //         $endtmonth = request('month', \Carbon\Carbon::now()->format('Y-m'));
    //     }


    //     $today = now()->day;
    //     $lastDay = now()->daysInMonth;

    //     $users = User::where('location_id', $location)->whereNotNull('rank')
    //         ->where('rank', '!=', '')->get();
    //     $packages = DB::table('mobile_packages')
    //         ->whereNotNull('num')
    //         ->orderBy('num', 'ASC')
    //         ->get();

    //     $results = [];

    //     foreach ($users as $user) {
    //         $userData = [
    //             'name' => $user->username,
    //             'rank' => $user->rank ?? 'N/A',
    //             'packages' => []
    //         ];

    //         $starget = match ($user->rank) {
    //             'A', null => 1,
    //             'B' => 2,
    //             'C' => 3,
    //             'D' => 4,
    //             default => 1,
    //         };

    //         foreach ($packages as $pkg) {
    //             $targetRow = DB::table('target_report')
    //                 ->whereBetween('date', [$startDate, $endDate])
    //                 ->where('package', $pkg->package_name)
    //                 ->where('type', 'Agent_Target')
    //                 ->where('quantity', '>', 0)
    //                 ->where('location', $location)
    //                 ->first();

    //             if ($targetRow) {
    //                 $requiredTarget = ceil($targetRow->quantity / $starget);

    //                 $mobileAchieved = DB::table('mobiles')
    //                     ->whereBetween('cashbox_date', [$startDate, $endDate])
    //                     ->where('user_id', $user->id)
    //                     ->where(function ($q) use ($pkg) {
    //                         $q->where('package_code', $pkg->id)
    //                             ->orWhere('addons', $pkg->id);
    //                     })
    //                     ->count();


    //                 $adslAchieved = DB::table('adsls')
    //                     ->whereBetween('cashbox_date', [$startDate, $endDate])
    //                     ->where('user_id', $user->id)
    //                     ->where('package_code', $pkg->id)
    //                     ->count();

    //                 // Count of New Landline
    //                 $landlineAchieved = DB::table('landlines')
    //                     ->whereBetween('cashbox_date', [$startDate, $endDate])
    //                     ->where('user_id', $user->id)
    //                     ->where('package_code', $pkg->id)
    //                     ->count();


    //                 // ======= 5. Total Achieved =======
    //                 $achieved = $mobileAchieved + $adslAchieved + $landlineAchieved;


    //                 $percentage = $requiredTarget > 0 ? round($achieved / $requiredTarget * 100, 1) : 0;
    //                 // Append to userData
    //                 $userData['packages'][$pkg->package_name] = $percentage;
    //             }
    //         }

    //         $results[] = $userData;
    //     }

    //     $allPackages = $packages->pluck('package_name')->unique();

    //     $monthlyTargets = DB::table('target_report')
    //         ->where('type', 'Agent_Target')
    //         ->whereBetween('date', [$startDate, $endDate])
    //         ->where('location', $location)
    //         ->where('quantity', '>', 0)
    //         ->orderBy('package')
    //         ->get();

    //     $locations = locations::all();
    //     $todaydate = date('Y-m-d');

    //     $locationname = locations::where('id',$location)->value('name_en');


    //     return view('reports.usertargetper', [
    //         'location' => $location,
    //         'locations' => $locations,
    //         'locationname' => $locationname,
    //         'startDate' => $startDate,
    //         'endDate' => $endDate,
    //         'startmonth' => $startmonth,
    //         'endtmonth' => $endtmonth,
    //         'date' => now()->format('F, Y'),
    //         'results' => $results,
    //         'allPackages' => $allPackages,
    //         'monthlyTargets' => $monthlyTargets,
    //     ]);
    // }





    public function MonthlyReport(Request $request)
    {


        $currentUser = Auth::user();

        $month = request('month', \Carbon\Carbon::now()->format('Y-m')); // Example full month string '2025-05';

        $location = !empty($request->query('location_id')) ? $request->query('location_id') : $currentUser->location_id;

        if (!empty($request->query('datefrom'))) {

            $startDate = $request->query('datefrom') . '-01';
            $endDate = Carbon::parse($startDate)->endOfMonth()->toDateString();

            $startmonth = $request->query('datefrom');
            $date = Carbon::parse($request->query('datefrom'));
        } else {

            $startDate = Carbon::now()->startOfMonth()->toDateString();
            $endDate = Carbon::now()->endOfMonth()->toDateString();

            $startmonth = request('month', \Carbon\Carbon::now()->format('Y-m'));

            $date = Carbon::createFromFormat('Y-m', $startmonth);
        }



        $locations = locations::all();
        $todaydate = date('Y-m-d');

        $locationname = locations::where('id', $location)->value('name_en');



        $days = collect(range(1, $date->daysInMonth))->map(fn($d) => $date->copy()->day($d)->toDateString());


        // dd( $days);


        // Helper to build daily counts
        $dailyCounts = function ($table, $dateField, $filterField, $filterValues) use ($startDate, $endDate) {
            return DB::table($table)
                ->selectRaw("DATE($dateField) as date, COUNT(*) as total")
                ->when($filterField && $filterValues, function ($q) use ($filterField, $filterValues) {
                    $q->whereIn($filterField, $filterValues);
                })

                ->whereBetween($dateField, [$startDate, $endDate])
                ->groupBy(DB::raw("DATE($dateField)"))
                ->pluck('total', 'date'); // ['2025-05-01' => 4, ...]
        };

        $mobileCounts = $dailyCounts('mobiles', 'cashbox_date', 'contract_type', ['Prepaid', 'Postpaid', 'Control']);
        $walletCounts = $dailyCounts('mobiles', 'cashbox_date', 'contract_type', ['Wallet']);
        $adslCounts   = $dailyCounts('adsls', 'cashbox_date', 'contract_type', ['New ADSL']);
        $landlineCounts = $dailyCounts('landlines', 'cashbox_date', 'contract_type', ['New Landline']);

        $mobileData = $days->map(fn($d) => $mobileCounts[$d] ?? 0);
        $walletData = $days->map(fn($d) => $walletCounts[$d] ?? 0);
        $adslData = $days->map(fn($d) => $adslCounts[$d] ?? 0);
        $landlineData = $days->map(fn($d) => $landlineCounts[$d] ?? 0);








        //  dd($mobileData);


        $user = Auth::user();

        $datos = $user->id;
        $location = $user->location_id;
        $title = $user->title;
        $rank = $user->rank;



        $starget = match ($rank) {
            'A', null => 1,
            'B' => 2,
            'C' => 3,
            'D' => 4,
            default => 1,
        };


        // Helper query function
        $targetSum = fn($conditions) =>
        DB::table('target_report')->selectRaw('SUM(quantity) as total')
            ->whereBetween('date', [$startDate, $endDate])
            ->where('type', 'Location_Target')
            ->where('quantity', '>', 0)

            ->where('location', $location)
            ->where($conditions)
            ->first()->total ?? 0;


        $countSum = fn($table, $conditions) =>
        DB::table($table)->selectRaw('COUNT(*) as total')
            ->whereBetween('cashbox_date', [$startDate, $endDate])
            ->where('location_id', $location)
            ->where($conditions)
            ->first()->total ?? 0;

        $targets = [
            'prepaid' => $targetSum(fn($q) => $q->whereIn('category', ['Prepaid', 'Control'])),
            'postpaid' => $targetSum(fn($q) => $q->where('category', 'Postpaid')),
            'adsl' => $targetSum(fn($q) => $q->where('category', 'ADSL')),
            'fixed' => $targetSum(fn($q) => $q->where('category', 'Fixed')),
            'wallet' => $targetSum(fn($q) => $q->where('category', 'Wallet')),
        ];

        $actuals = [
            // 'prepaid' => $countSum('mobiles', fn($q) => $q->whereIn('contract_type', ['Control', 'Prepaid'])
            //     ->whereNotIn('addons', ['MNP_IN', 'MNP_OUT'])->orWhereNull('addons')),

            'prepaid' => $countSum('mobiles', fn($q) => $q->whereIn('contract_type', ['Control', 'Prepaid'])),
            'postpaid' => $countSum('mobiles', fn($q) => $q->where('contract_type', 'Postpaid')),
            'adsl' => $countSum('adsls', fn($q) => $q->where('contract_type', 'New ADSL')),
            'fixed' => $countSum('landlines', fn($q) => $q->where('contract_type', 'New Landline')),
            'wallet' => $countSum('mobiles', fn($q) => $q->where('contract_type', 'Wallet')),
        ];

        $percentages = [];
        foreach ($targets as $key => $val) {

            $required = max(1, round($val / $starget));

            $achieved = $actuals[$key];

            $percentages[$key] = [
                'requiredtar' => $required,
                'achievedtar' => $achieved,
                'percentage' => round(($achieved / $required) * 100, 1),
                'display' => round(($achieved / $required) * 100)
            ];
        }



        $today = now()->day;
        $lastDay = now()->daysInMonth;
        $packages = DB::table('mobile_packages')->whereNotNull('num')->orderBy('num', 'ASC')->get();


        $results = [];

        foreach ($packages as $pkg) {
            $row = ['package' => $pkg->package_name];

            // MOBILE category
            $target = DB::table('target_report')->whereBetween('date', [$startDate, $endDate])
                ->where('package', $pkg->package_name)
                ->where('type', 'Location_Target')
                ->where('location', $location)
                ->where('quantity', '>', 0)
                ->first();
            if ($target) {
                $requiredTarget = ceil($target->quantity / $starget);

                $mobileAchieved = DB::table('mobiles')
                    ->whereBetween('cashbox_date', [$startDate, $endDate])
                    ->where('location_id', $location)
                    ->where(function ($q) use ($pkg) {
                        $q->where('package_code', $pkg->id)
                            ->orWhere('addons', $pkg->id);
                    })->count();

                $adslAchieved = DB::table('adsls')
                    ->whereBetween('cashbox_date', [$startDate, $endDate])
                    ->where('location_id', $location)
                    ->where('package_code', $pkg->id)
                    ->count();

                // Count of New Landline
                $landlineAchieved = DB::table('landlines')
                    ->whereBetween('cashbox_date', [$startDate, $endDate])
                    ->where('location_id', $location)
                    ->where('package_code', $pkg->id)
                    ->count();


                // ======= 5. Total Achieved =======
                $achieved = $mobileAchieved + $adslAchieved + $landlineAchieved;






                $projection = ($requiredTarget > 0 && $today > 0)
                    ? round(($achieved / $today * $lastDay) / $requiredTarget * 100, 1) . '%'
                    : '0%';

                $row += [
                    'target' => $requiredTarget,
                    'achieved' => $achieved,
                    'required' => $achieved - $requiredTarget,
                    'projection' => $projection,
                ];
            }

            // Repeat similar blocks for ADSL, Fixed (landline), Device using adjusted tables/logic

            $results[] = $row;
        }

        return view('reports/locationreport', [
            'categories' => $days->map(fn($d) => Carbon::parse($d)->format('d')),
            'mobileData' => $mobileData,
            'adslData' => $adslData,
            'landlineData' => $landlineData,
            'walletData' => $walletData,
            'location' => $location,
            'date' => Carbon::now()->format('F, Y'),
            'locations' => $locations,
            'locationname' => $locationname,
            'startDate' => $startDate,
            'endDate' => $endDate,
            'startmonth' => $startmonth,

            'percentages' => $percentages,
            'results' => $results,



        ]);



        // return view('/dashboard', [
        //     'categories' => $daysInMonth->keys()->map(fn($i) => str_pad($i + 1, 2, '0', STR_PAD_LEFT)),
        //     'mobileData' => $daysInMonth->values(),
        //     'adslData' => [25, 55, 35, 50, 45, 20, 31],
        //     'landlineData' => [45, 15, 35, 70, 45, 50, 21]
        // ]);
    }

    public function AreaReport()
    {
        // Default to current month and group by area_name
        $startDate = now()->startOfMonth()->format('Y-m-d');
        $endDate = now()->endOfMonth()->format('Y-m-d');
        $groupBy = 'area_name';

        return $this->generateAreaReport($startDate, $endDate, $groupBy);
    }

    public function AreaReportFilter(Request $request)
    {
        $startDate = $request->input('date_from', now()->startOfMonth()->format('Y-m-d'));
        $endDate = $request->input('date_to', now()->endOfMonth()->format('Y-m-d'));
        $groupBy = $request->input('group_by', 'area_name');

        return $this->generateAreaReport($startDate, $endDate, $groupBy);
    }

    private function generateAreaReport($startDate, $endDate, $groupBy = 'area_name')
    {
        // Validate groupBy field
        $validFields = ['area', 'area_name', 'sector_name'];
        if (!in_array($groupBy, $validFields)) {
            $groupBy = 'area_name';
        }

        // Get all areas with their locations
        $areas = DB::table('locations')
            ->select($groupBy)
            ->whereNotNull($groupBy)
            ->where($groupBy, '!=', '')
            ->distinct()
            ->orderBy($groupBy)
            ->get();

        $results = [];

        foreach ($areas as $area) {
            $areaName = $area->$groupBy;

            // Get all location IDs for this area
            $locationIds = DB::table('locations')
                ->where($groupBy, $areaName)
                ->pluck('id')
                ->toArray();

            if (empty($locationIds)) {
                continue;
            }

            // Count mobiles for this area
            $mobileCount = DB::table('mobiles')
                ->whereBetween('cashbox_date', [$startDate, $endDate])
                ->whereIn('location_id', $locationIds)
                ->count();

            // Count ADSLs for this area
            $adslCount = DB::table('adsls')
                ->whereBetween('cashbox_date', [$startDate, $endDate])
                ->whereIn('location_id', $locationIds)
                ->count();

            // Count landlines for this area
            $landlineCount = DB::table('landlines')
                ->whereBetween('cashbox_date', [$startDate, $endDate])
                ->whereIn('location_id', $locationIds)
                ->count();

            // Sum devices price for this area (handle null values)
            $devicesSum = DB::table('devices')
                ->whereBetween('cashbox_date', [$startDate, $endDate])
                ->whereIn('location_id', $locationIds)
                ->sum('price') ?? 0;

            // Get location names for this area (filter out null/empty names)
            $locations = DB::table('locations')
                ->where($groupBy, $areaName)
                ->whereNotNull('name_en')
                ->where('name_en', '!=', '')
                ->pluck('name_en')
                ->toArray();

            $results[] = [
                'area_name' => $areaName,
                'locations' => $locations,
                'location_count' => count($locations),
                'mobile_count' => $mobileCount,
                'adsl_count' => $adslCount,
                'landline_count' => $landlineCount,
                'devices_sum' => (float) $devicesSum,
                'total_services' => $mobileCount + $adslCount + $landlineCount,
            ];
        }

        // Sort by total services descending, then by area name
        usort($results, function($a, $b) {
            if ($b['total_services'] == $a['total_services']) {
                return strcmp($a['area_name'], $b['area_name']);
            }
            return $b['total_services'] - $a['total_services'];
        });

        // Calculate totals
        $totalMobiles = array_sum(array_column($results, 'mobile_count'));
        $totalAdsls = array_sum(array_column($results, 'adsl_count'));
        $totalLandlines = array_sum(array_column($results, 'landline_count'));
        $totalDevicesSum = array_sum(array_column($results, 'devices_sum'));
        $totalServices = $totalMobiles + $totalAdsls + $totalLandlines;

        // Get the display name for the grouping field
        $groupByDisplayName = match($groupBy) {
            'area' => 'Area',
            'area_name' => 'Area Name',
            'sector_name' => 'Sector Name',
            default => 'Area Name'
        };

        return view('reports.areareport', [
            'results' => $results,
            'startDate' => $startDate,
            'endDate' => $endDate,
            'groupBy' => $groupBy,
            'groupByDisplayName' => $groupByDisplayName,
            'dateRange' => \Carbon\Carbon::parse($startDate)->format('M d, Y') . ' - ' . \Carbon\Carbon::parse($endDate)->format('M d, Y'),
            'totals' => [
                'mobile_count' => $totalMobiles,
                'adsl_count' => $totalAdsls,
                'landline_count' => $totalLandlines,
                'devices_sum' => $totalDevicesSum,
                'total_services' => $totalServices,
                'area_count' => count($results),
            ],
        ]);
    }
}
