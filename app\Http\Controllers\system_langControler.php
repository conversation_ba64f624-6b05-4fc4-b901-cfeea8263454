<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\system_langs;

class system_langControler extends Controller
{
    public function langcon() {


        $langresultb = system_langs::all();
    
    }


    public function SysLang()
    {
        $langresultb = system_langs::orderBy('id', 'desc')->get();

        return view('/translation', ['syslangs' => $langresultb]);
    }



    public function AddNewLang(Request $request)
    {

     


        if ($request->filled('cusappid')) {
            $request->validate([
                'upenglish' => 'required',

            ]);
            
            $currectnang = system_langs::find($request->cusappid);
            $currectnang->english  = $request->upenglish;
            $currectnang->german   = $request->upgerman;
            $currectnang->arabic   = $request->uparabic;
            $currectnang->save();

        } else {

            $request->validate([
                'lang_key' => ['required', 'unique:system_langs', 'max:255'],
                'englishlang' => 'required',

            ]);


            $newcategory = new system_langs;
            $newcategory->lang_key = $request->lang_key;
            $newcategory->english  = $request->englishlang;
            $newcategory->german   = $request->germanlang;
            $newcategory->arabic   = $request->arabiclang;
            $newcategory->save();

        }


        return redirect()->back()->with('success', 'Product added to cart!');



    }
}
