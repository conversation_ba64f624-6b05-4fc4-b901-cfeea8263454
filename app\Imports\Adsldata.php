<?php

namespace App\Imports;

use App\Models\User;
use App\Models\cashbox;
use App\Models\adsl;
use App\Models\devices;
use App\Models\mobile;
use App\Models\landline;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;



class Adsldata implements ToModel
{
    /**
     * @param array $row
     *
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public $totalVisa = 0;
    public $totalCash = 0;
    public $totalCheque = 0;
    public $totaltax = 0;
    public $totalbank = 0;
    public $totalvat = 0;
    public $totalCountmobile = 0;

    public $totalCountprepaid = 0;
    public $totalCountpostpaid = 0;
    public $totalCountdata = 0;

    public $totalCountadsl = 0;
    public $totalCountlandline = 0;
    public $totalCountdevice = 0;
    public $totalCountreceipt = 0;
    public $totalCounttransaction = 0;
    public $totalcashbox = 0;


    public function model(array $row)   
    {

        $user = Auth::user();
        $username = $user->id;

        $cashboxdate = trim($row[0]);
        $snumber = trim($row[1]);
        $contracttype = trim($row[3]);
        $package = trim($row[4]);
        $username = trim($row[4]);


// $convertedDate = Carbon::createFromDate(1900, 1, 1)->addDays($cashboxdate - 2);



// Format as Y-m-d
// $formatted = $convertedDate->format('Y-m-d');




        // Check if the number already exists in the database
        if ($username ) {

            // $exists = mobile::where('number', $snumber)
            //     ->exists();


            $useriddata = User::where('username',$username)->value('id');

                     if($useriddata){


                adsl::create([
                    'cashbox_date' => $cashboxdate,
                    'number'       => $snumber,
                    'contract_type' => 'New ADSL',
                    'package_code' => 14,
                    'location_id' => 1,
                    'user_id' => $useriddata,


                ]);

            }
        }





    }
}
