<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('roles_permisstions', function (Blueprint $table) {
            $table->id();

            $table->unsignedBigInteger('role_id'); // Foreign key must be UNSIGNED
            $table->foreign('role_id')->references('id')->on('roles')->onDelete('cascade');
            $table->string('tableName')->nullable();
            $table->string('allowInsert')->nullable();
            $table->string('allowView')->nullable();
            $table->string('allowEdit')->nullable();
            $table->string('allowDelete')->nullable();
            $table->string('import')->nullable();
            $table->string('export')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('roles_permisstions');
    }
};
