<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Imports\StockImport;
use App\Imports\Cashboxdata;
use App\Imports\Walletdata;
use App\Imports\Mobiledata;
use App\Imports\Adsldata;
use App\Models\cashbox;
use App\Models\wallet_cashbox;

use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;

use App\Exports\CashierCustomExport;

use App\Exports\scheduleexport;

use App\Imports\ScheduleImport;

use App\Exports\MobileExport;
use App\Exports\ADSLExport;
use App\Exports\LandlineExport;
use App\Exports\DevicesExport;

class ExcelController extends Controller
{


public function ImportSchedule(Request $request)
{

    $request->validate([
        'schedulefile' => 'required|mimes:xlsx,xls',

    ]);

$month = '05';
$year = '2025';

    Excel::import(new ScheduleImport($month, $year), $request->file('schedulefile'));

    return back()->with('success', 'Schedule imported successfully!');
}

public function exportMobile(Request $request)
{
    return Excel::download(new MobileExport($request), 'Mobile_' . now()->format('d-m-Y') . '.xlsx');
}

public function exportADSL(Request $request)
{
    return Excel::download(new ADSLExport($request), 'ADSL_' . now()->format('d-m-Y') . '.xlsx');
}

public function exportLandline(Request $request)
{
    return Excel::download(new LandlineExport($request), 'Landline_' . now()->format('d-m-Y') . '.xlsx');
}

public function exportDevices(Request $request)
{
    return Excel::download(new DevicesExport($request), 'Devices_' . now()->format('d-m-Y') . '.xlsx');
}

public function ExportSchedule(Request $request)
{
    return Excel::download(new scheduleexport($request), 'Schedule_' . now()->format('d-m-Y') . '.xlsx');
}

public function viewStockUpload()
{
    return view('/excel_uploads/stock_upload');
}

public function viewMobileUpload()
{
    return view('/excel_uploads/uploadmobile');
}

public function viewAdslUpload()
{
    return view('/excel_uploads/uploadadsl');
}

public function UploadCashbox()
{
    return view('/excel_uploads/uploadcashbox');
}

public function StockUpload(Request $request)
{
    $request->validate([
        'file' => 'required|mimes:xlsx,xls,csv'
    ]);

    Excel::import(new StockImport($request->type), $request->file('file'));

    return back()->with('success', 'Data Imported Successfully');
}

public function CashBoxData(Request $request)
{
    $request->validate([
        'file' => 'required|mimes:xlsx,xls,csv'
    ]);

    $user = Auth::user();
    $username = $user->id;
    $useremail = $user->email;
    $userlocation = $user->location_id;
    $datetoday = date('Y-m-d');

    $exists = cashbox::where('cashbox_date', $datetoday)
        ->where('user_id', $username)
        ->exists();

    if ($exists) {
        cashbox::where('cashbox_date', $datetoday)->where('user_id', $username)->delete();
    }

    $import = new Cashboxdata();
    Excel::import($import, $request->file('file'));

    $MobileAchieved = DB::table('mobiles')
        ->where('cashbox_date', $datetoday)
        ->where('location_id', $userlocation)
        ->whereIn('contract_type', ['Control', 'Prepaid', 'Postpaid'])
        ->count();

    if ($MobileAchieved === $import->totalCountmobile) {
        $mobilemiss = 0;
    } else {
        $mobilemiss = $MobileAchieved - $import->totalCountmobile;
    }

    cashbox::create([
        'cashbox_date'   => $datetoday,
        'prepaid_sales'  => $import->totalCountprepaid - $import->totalCountdata,
        'data_mix_sales' => $import->totalCountdata,
        'postpaid_sales' => $import->totalCountpostpaid,
        'adsl_sales'     => $import->totalCountadsl,
        'fixed_sales'    => $import->totalCountlandline,
        'total_receipts' => $import->totalCountreceipt - 1,
        'transactions'   => $import->totalCounttransaction - 1,
        'device_amount'  => $import->totalCountdevice,
        'cashbox_amount' => $import->totalCash  + $import->totalVisa,
        'location_id'    => $userlocation,
        'user_id'        => $username,
    ]);

    return response()->json([
        'success' => true,
        'message' => 'Imported successfully',
        'total_cash' => $import->totalCash,
        'total_visa' => $import->totalVisa,
        'total_cheque' => $import->totalCheque,
        'total_tax' => $import->totaltax,
        'total_vat' => $import->totalvat,
        'total_bank' => $import->totalbank,
        'mobilemiss' => $mobilemiss,
    ]);
}

public function Walletdata(Request $request)
{
    $request->validate([
        'file' => 'required|mimes:xlsx,xls,csv'
    ]);

    $user = Auth::user();
    $username = $user->id;
    $useremail = $user->email;
    $userlocation = $user->location_id;

    $import = new Walletdata();
    Excel::import($import, $request->file('file'));

    return response()->json([
        'success' => true,
        'message' => 'Imported successfully',
        'total_walletin' => $import->totalCashin,
        'total_walletout' => $import->totalCashout,
    ]);
}

public function Mobiledata(Request $request)
{
    $request->validate([
        'file' => 'required|mimes:xlsx,xls,csv'
    ]);

    $user = Auth::user();
    $username = $user->id;
    $useremail = $user->email;
    $userlocation = $user->location_id;

    $import = new Mobiledata();
    Excel::import($import, $request->file('file'));

    return response()->json([
        'success' => true,
        'message' => 'Imported successfully',
    ]);
}

public function ADSLdata(Request $request)
{
    $request->validate([
        'file' => 'required|mimes:xlsx,xls,csv'
    ]);

    $user = Auth::user();
    $username = $user->id;
    $useremail = $user->email;
    $userlocation = $user->location_id;

    $import = new Adsldata();
    Excel::import($import, $request->file('file'));

    return response()->json([
        'success' => true,
        'message' => 'Imported successfully',
    ]);
}

public function DeleteAll()
{
    DB::table('warehouses')->where('id', '>', 0)->delete();
    return view('/excel_uploads/stock_upload');
}

public function DeleteAllMobile()
{
    DB::table('mobiles')->where('id', '>', 0)->delete();
    return view('/excel_uploads/uploadmobile');
}

public function exportCashierReport(Request $request)
{
    $date = $request->cashdate;
    $location = $request->location;
    return new \App\Exports\CashierCustomExport($date, $location);
}
}


