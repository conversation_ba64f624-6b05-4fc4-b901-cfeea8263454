<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\performance;
use App\Models\locations;
use App\Models\user;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Hash;


class AccountController extends Controller
{

    public function AgentPerformance()
    {

        // $packagesdata = performance::orderBy('created_at', 'desc')->get();
        $alllocations = locations::orderBy('created_at', 'desc')->paginate(30);

        $usernames = User::orderBy('created_at', 'desc')->get();


        if (in_array(Auth::user()->role_name, ['Agent', 'Leader', 'Senior', 'Supervisor'])) {

            $query = performance::where('location_id', Auth::user()->location_id);

            if (in_array(Auth::user()->role_name, ['Agent', 'Leader'])) {

                $query->where('user_id', Auth::user()->user_id);
            }

            $packagesdata = $query->orderBy('created_at', 'desc')->get();
        } else {
            $packagesdata = performance::orderBy('created_at', 'desc')->get();
        }





        return view('/agperformance', ['performance' => $packagesdata, 'locations' => $alllocations, 'usernames' => $usernames, 'searchcat' => '']);
    }


    public function updatePassword(Request $request)
    {
        $request->validate([
            'currentpassword' => 'required|string',
            'newpassword' => 'required|string|min:8|confirmed', // 'confirmed' expects a field named 'newpassword_confirmation'
        ]);

        $user = \App\Models\User::find(Auth::id());

        // Check if current password matches
        if (!Hash::check($request->currentpassword, $user->password)) {
            return back()->withErrors(['currentpassword' => 'Current password is incorrect.']);
        }

        // Update password
        $user->password = Hash::make($request->newpassword);
        $user->save();

        return back()->with('status', 'Password updated successfully.');
    }


    public function AgentStorePer(Request $request)

    {
        if ($request->memberid) {



            $userdata = User::find($request->username);


            $perform = performance::find($request->memberid);
            $perform->action_date = $request->action_date;
            $perform->title = $request->title;
            $perform->type = $request->type;

            $perform->comment = $request->comment;
            $perform->user_id  = $userdata->id;
            $perform->location_id  = $userdata->location_id;




            if ($request->hasFile('upfile') && $request->file('upfile')->isValid()) {
                $image = $request->file('upfile');

                // Generate a unique name for the image
                $uniqueName = Str::random(20) . '.' . $image->getClientOriginalExtension();

                // Store the image in the 'images' folder of 'public' disk
                $path = $image->storeAs('upload', $uniqueName, 'public');

                $perform->profile_pic = $path;
            }


            $perform->save();
        } else {



            $userdata = User::find($request->username);


            $perform = new performance;

            $perform->action_date = $request->action_date;
            $perform->title = $request->title;
            $perform->type = $request->type;

            $perform->comment = $request->comment;
            $perform->user_id  = $userdata->id;
            $perform->location_id  = $userdata->location_id;



            if ($request->hasFile('upfile') && $request->file('upfile')->isValid()) {
                $image = $request->file('upfile');

                // Generate a unique name for the image
                $uniqueName = Str::random(20) . '.' . $image->getClientOriginalExtension();

                // Store the image in the 'images' folder of 'public' disk
                $path = $image->storeAs('upload', $uniqueName, 'public');
                $perform->upload = $path;
            }


            $perform->save();
        }

        return redirect()->back()->with('success', 'Product added to cart!');
    }


    public function ChangeLang($lang = null)
    {

        if ($lang) {

            App::setLocale($lang); // Set the locale for current request

            Session()->put('locale', $lang);

            return redirect()->back()->with('success');
        }
    }


    public function Deletecperform($proid = null)
    {


        if ($proid) {

            $currectcartdata = performance::find($proid);

            $currectcartdata->delete();
        } else {


            abort(403, 'Unauthorized action.');
        }
        return redirect()->back()->with('success', 'Performance record deleted successfully!');
    }




    public function AccountEditProfile(Request $request, $memid = null)
    {


        $request->validate([
            'avatar' => 'image|mimes:jpeg,png,jpg,gif,svg|max:2048',

        ]);

        $currectuser = User::find(Auth::user()->id);

        $currectuser->language = $request->user_lang;

        if ($request->hasFile('avatar') && $request->file('avatar')->isValid()) {
            $image = $request->file('avatar');

            // Generate a unique name for the image
            $uniqueName = Str::random(20) . '.' . $image->getClientOriginalExtension();

            // Store the image in the 'images' folder of 'public' disk
            $path = $image->storeAs('profile_pic', $uniqueName, 'public');

            $currectuser->profile_pic = $path;
        }


        $currectuser->save();


        return redirect()->back()->with('success', 'Performance record deleted successfully!');
    }
}
