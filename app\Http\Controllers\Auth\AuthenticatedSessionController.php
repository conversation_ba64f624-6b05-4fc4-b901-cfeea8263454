<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\LoginRequest;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\View\View;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Carbon;
use App\Models\logs;
use App\Models\alerts;
use App\Models\locations;
use Illuminate\Support\Facades\Schema;
use App\Models\User;

class AuthenticatedSessionController extends Controller
{
    /**
     * Display the login view.
     */
    public function create(): View
    {

        return view('auth.login');
    }

    /**
     * Handle an incoming authentication request.
     */
    public function store(LoginRequest $request): RedirectResponse
    {


        $request->authenticate();
        $user = Auth::user();

        User::where('id', $user->id)->update(['session_start' => NULL]);



        $todaytime = date("H:i:s");
        $todaydate = date("Y-m-d");
        $day = date('D', strtotime($todaydate));
        $schedtable  = str_replace(".", "_", $user->username);
        $evall = 0;
        $ipAddress = request()->ip(); // anywhere else
        // Default values
        $evall = 0;
        $comment = null;

        if (logs::where('date', $todaydate)->where('user_id', Auth::id())->exists()) {
            // logs::where('date', $todaydate)->update(['action' => 'User logged in again']);
        } else {



            if (Schema::hasColumn('sched', $schedtable)) {
                $user_shift = DB::table('sched')
                    ->where('date', $todaydate)
                    ->value($schedtable);




                if ($user_shift) {

                    if (in_array($user_shift, ['Regular_AM', 'Regular_BW', 'Regular_PM', 'Shift_AM', 'Shift_PM'])) {

                        $location_data = locations::find($user->location_id);

                        // Set shift time based on shift type
                        switch ($user_shift) {
                            case 'Regular_AM':
                                $shiftTime = strtotime($day === 'Fri' ? $location_data->fullshift_shift : $location_data->am_shift);
                                break;

                            case 'Regular_BW':

                                $shiftTime = strtotime($day === 'Fri' ? $location_data->fullshift_shift : $location_data->bw_shift);

                                break;

                            case 'Regular_PM':

                                if ($day === 'Fri') {
                                    $shiftTime = strtotime($location_data->fullshift_shift);
                                } elseif ($day === 'Thu') {
                                    $shiftTime = strtotime($location_data->regular_shift);
                                } else {
                                    $shiftTime = strtotime($location_data->pm_shift);
                                }

                                break;

                            case 'Shift_AM':

                                $shiftTime = strtotime($day === 'Fri' ? $location_data->fullshift_shift : $location_data->fullshift_am);

                                break;

                            case 'Shift_PM':


                                if ($day === 'Fri') {
                                    $shiftTime = strtotime($location_data->fullshift_shift);
                                } elseif ($day === 'Thu') {
                                    $shiftTime = strtotime($location_data->regular_shift);
                                } else {
                                    $shiftTime = strtotime($location_data->fullshift_pm);
                                }


                                break;
                        }

                        // Attendance evaluation windows
                        $inam1 = date('H:i:s', $shiftTime - 15 * 60); // 15 minutes early
                        $inam2 = date('H:i:s', $shiftTime - 10 * 60); // 10 minutes early
                        $inam3 = date('H:i:s', $shiftTime - 5 * 60);  // 5 minutes early



                        // Evaluate based on attendance time
                        if ($todaytime <= $inam1) {
                            $evall = 1; // On time
                        } elseif ($todaytime <= $inam2) {
                            $evall = 0.5; // Slightly late
                        } elseif ($todaytime < $inam3) {
                            $evall = 0.25; // Very late
                        } else {
                            $evall = 0;
                            $comment = 'Late Attendance';
                        }
                    }
                }
            } else {

                $user_shift = 'No Shift';
                $evall = 0;
                $comment = 'No Shift';
            }


            logs::create([
                'date' => $todaydate,
                'day' => $day,
                'user_id' => Auth::id(),
                'signin' => $todaytime,
                'nickname' => $user->nick_name,
                'location_id' =>  $user->location_id,
                'location' =>  $user->location_name,
                'eval' =>   $evall,
                'shift' =>   $user_shift,
                'ip' =>   $ipAddress,
                'comment' =>   $comment ?? null,
                'username' => $user->username


            ]);


            if ($comment === 'Late Attendance') {



                $monthStart = now()->startOfMonth()->toDateString();
                $monthEnd   = now()->endOfMonth()->toDateString();
                $annualFrom = now()->startOfYear()->toDateString();
                $annualTo = now()->subDay()->toDateString();


                $targetAchieved = DB::table('logs')
                    ->whereBetween('Date', [$monthStart, $monthEnd])
                    ->where('user_id', Auth::id())
                    ->where('comment', 'Late Attendance')
                    ->count();

                $num1 = DB::table('sched')
                    ->whereBetween('date', [$annualFrom, $annualTo])
                    ->whereRaw("SUBSTRING(`$schedtable`, -12) = 'Annual_Leave'")
                    ->count();

                $num1 += 1;


                if ($targetAchieved === 0) {
                    $permission = 'First Time';
                } elseif ($targetAchieved === 1) {
                    $permission = 'Second Time';
                } elseif ($targetAchieved === 2) {
                    $permission = 'Third Time';
                } elseif ($targetAchieved === 3) {
                    $permission = 'Half Day';
                    $newcomm = "$num1|0.5|Late|Annual_Leave";
                } elseif ($targetAchieved >= 4) {
                    $permission = 'One Days';
                    $newcomm = "$num1|1.0|Late|Annual_Leave";
                }

                if ($newcomm !== null) {
                    DB::table('sched')
                        ->where('date', $todaydate)
                        ->update([$schedtable => $newcomm]);
                }

                alerts::create([
                    'event_date' => $todaydate,
                    'type' => 'Attendance',
                    'user_id' => Auth::id(),
                    'comment' => 'Late Attendance',
                    'location_id' =>  $user->location_id

                ]);
            }
        }



        $request->session()->regenerate();


        return redirect()->intended(route('dashboard', absolute: false));
    }

    /**
     * Destroy an authenticated session.
     */
    public function destroy(Request $request): RedirectResponse
    {


        $user = Auth::user();

        $todaytime = date("H:i:s");
        $todaydate = date("Y-m-d");



        $exists = logs::where('date', $todaydate)
            ->where('user_id', $user->id)
            ->exists();

        if ($exists) {


            logs::where('date', $todaydate)
                ->where('user_id', $user->id)
                ->update([
                    'signout' =>  $todaytime, // replace with actual columns
                    'updated_at' => now(), // optional
                ]);
        }


        Auth::guard('web')->logout();

        $request->session()->invalidate();

        $request->session()->regenerateToken();

        return redirect('/login');
    }
}
