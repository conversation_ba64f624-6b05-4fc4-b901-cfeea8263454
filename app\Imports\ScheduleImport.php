<?php

namespace App\Imports;

use App\Models\User;
use Maatwebsite\Excel\Concerns\ToModel;
use Illuminate\Support\Facades\DB;

use App\Models\Schedule;
use Carbon\Carbon;
use Maatwebsite\Excel\Row;
use Maatwebsite\Excel\Concerns\OnEachRow;
use Maatwebsite\Excel\Concerns\WithHeadingRow;

class ScheduleImport implements OnEachRow, WithHeadingRow
{
    protected $month, $year;
    protected $columnToEmp = []; // Maps column name to emp number
    protected $isMappingRowProcessed = false;

    protected $lastDate = null;
    protected $lastDayName = null;

    public function __construct($month, $year)
    {
        $this->month = $month;
        $this->year = $year;
    }

    public function onRow(Row $row)
    {
        $row = array_change_key_case($row->toArray(), CASE_LOWER);

        // Step 1: Get the row with employee numbers
        if (!$this->isMappingRowProcessed) {
            foreach ($row as $col => $emp) {
                if (!in_array($col, ['date', 'day']) && $emp) {
                    $this->columnToEmp[$col] = $emp;
                }
            }
            $this->isMappingRowProcessed = true;
            return;
        }

        // Step 2: Process schedule rows
        $date = $row['date'] ?? null;
        $dayName = $row['day'] ?? null;

        if ($date) $this->lastDate = $date;
        if ($dayName) $this->lastDayName = $dayName;

        if (!$this->lastDate || !$this->lastDayName) return;

        foreach ($row as $colName => $userdata) {
            if (in_array($colName, ['date', 'day'])) continue;

            $emp = $this->columnToEmp[$colName] ?? null;
            if (!$emp) continue;

            $user = DB::table('users')->where('emp_num', $emp)->first();
            if (!$user) continue;

            $memberID = str_replace('.', '_', $user->username);

            $date = Carbon::createFromDate(1899, 12, 30)->addDays((int) $this->lastDate)->format('Y-m-d');



            DB::table('sched')->where('date', $date)->update([
                $memberID => $userdata
            ]);
        }
    }
}
