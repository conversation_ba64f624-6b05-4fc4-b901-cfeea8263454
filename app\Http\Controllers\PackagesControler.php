<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\mobile_packages;

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;


class PackagesControler extends Controller
{
    public function GetMobilePackage($packid = null)
    {

        $packagesdata = mobile_packages::findorfail($packid);

        return response()->json($packagesdata);
    }


    public function PackagesView()
    {

        $packagesdata = mobile_packages::orderBy('created_at', 'desc')->paginate(30);



        return view('packages', ['packages' => $packagesdata, 'searchcat' => '']);
    }



    public function StorePackage(Request $request)
    {

        $user = Auth::user();
        $username = $user->id;
        $todatdate = date('Y-m-d');
        if ($request->cusappid) {

            $request->validate([
                'package_name' => ['required', 'max:255'],
                'package_type' => 'required',

            ]);


            $currectlandline = mobile_packages::find($request->cusappid);
            $currectlandline->package_name = $request->package_name;
            $currectlandline->package_type = $request->package_type;
            $currectlandline->category  = $request->category;
            $currectlandline->num  = $request->num;
            $currectlandline->save();

        } else {

            $request->validate([
                'package_name' => ['required', 'max:255'],
                'package_type' => 'required',

            ]);




            $currectlandline = new mobile_packages;
            $currectlandline->package_name = $request->package_name;
            $currectlandline->package_type = $request->package_type;
            $currectlandline->category  = $request->category;
            $currectlandline->num  = $request->num;


            $currectlandline->save();
        }


        return redirect()->back()->with('success', 'Your action was successful!');
    }



}
