<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cashier_reports', function (Blueprint $table) {
            $table->id();

            $table->date('cashbox_date')->nullable();


            $table->float('bss_cash', 1, 2)->nullable();
            $table->float('bss_visa', 1, 2)->nullable();
            $table->float('oss_cash', 1, 2)->nullable();
            $table->float('oss_visa', 1, 2)->nullable();
            $table->float('wallet_in', 1, 2)->nullable();
            $table->float('wallet_out', 1, 2)->nullable();
            $table->float('e_payment', 1, 2)->nullable();
            $table->float('vat', 1, 2)->nullable();

            $table->float('tax', 1, 2)->nullable();
            $table->float('chique', 1, 2)->nullable();
            $table->float('telegraph_cash', 1, 2)->nullable();
            $table->float('telegraph_visa', 1, 2)->nullable();

            
            $table->float('delta_cash', 1, 2)->nullable();
            $table->float('delta_visa', 1, 2)->nullable();
            $table->string('pints')->nullable();
            $table->float('points_amount', 1, 2)->nullable();

            $table->float('total_cash', 1, 2)->nullable();
            $table->float('total_visa', 1, 2)->nullable();
            $table->float('total', 1, 2)->nullable();
            $table->float('total_cashbox', 1, 2)->nullable();
            $table->string('upload')->nullable();
            $table->string('comment',800)->nullable();

            $table->unsignedBigInteger('location_id')->nullable();
            $table->foreign('location_id')->references('id')->on('locations')->onDelete('cascade');
            $table->unsignedBigInteger('user_id')->nullable();
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');




            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cashier_reports');
    }
};
