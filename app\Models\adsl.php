<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class adsl extends Model
{
    protected $fillable = [
        'number',
        'contract_type',
        'cashbox_date',
        'comment',
        'package_code',
        'location_id',
        'user_id',
        'status',
        // add other fields if needed later
    ];

    public function packages()
    {
        return $this->belongsTo(mobile_packages::class, 'package_code', 'id');
    }



    public function products()
    {
        return $this->belongsTo(mobile_products::class, 'product_code', 'id');
    }

    public function users()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }


    public function location()
    {
        return $this->belongsTo(locations::class, 'location_id', 'id');
    }



}

