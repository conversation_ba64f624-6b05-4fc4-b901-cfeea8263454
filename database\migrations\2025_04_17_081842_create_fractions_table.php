<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('fractions', function (Blueprint $table) {
            $table->id();

            $table->date('cashbox_date')->nullable();


            $table->float('C_200', 1, 2)->nullable();
            $table->float('C_100', 1, 2)->nullable();
            $table->float('C_50', 1, 2)->nullable();
            $table->float('C_20', 1, 2)->nullable();
            $table->float('C_10', 1, 2)->nullable();
            $table->float('C_5', 1, 2)->nullable();
            $table->float('C_1', 1, 2)->nullable();
            $table->float('C_0_5', 1, 2)->nullable();
            $table->float('C_0_25', 1, 2)->nullable();
            $table->float('total', 1, 2)->nullable();
            $table->float('amount', 1, 2)->nullable();
            $table->float('short', 1, 2)->nullable();
            $table->string('description',800)->nullable();


            
            $table->unsignedBigInteger('location_id')->nullable();
            $table->foreign('location_id')->references('id')->on('locations')->onDelete('cascade');
            $table->unsignedBigInteger('user_id')->nullable();
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->string('status')->nullable()->default('Pending');

            $table->timestamps();

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('fractions');
    }
};
