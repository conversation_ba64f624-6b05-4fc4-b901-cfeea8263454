<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('app_details', function (Blueprint $table) {
            $table->id();
            $table->string('section')->nullable();
            $table->string('note')->nullable();
            $table->string('route')->nullable();
            $table->string('text',800)->nullable();
            $table->string('image')->nullable();



            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('app_details');
    }
};
