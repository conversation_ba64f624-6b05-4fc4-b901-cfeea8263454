<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('locations', function (Blueprint $table) {
            $table->id();


            $table->string('name_en')->nullable();
            $table->string('name_ar')->nullable();
            $table->string('location_type')->nullable();
            $table->string('location_code')->nullable();
            $table->string('direct_manager')->nullable();
            $table->string('division')->nullable();
            $table->string('area')->nullable();

            $table->string('area_name')->nullable();
            $table->string('sector_name')->nullable();


            $table->time('store_from')->nullable();
            $table->time('store_to')->nullable();

            $table->time('am_shift')->nullable();
            $table->time('pm_shift')->nullable();
            $table->time('bw_shift')->nullable();

            $table->time('fullshift_shift')->nullable();
            $table->time('regular_shift')->nullable();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('locations');
    }
};
