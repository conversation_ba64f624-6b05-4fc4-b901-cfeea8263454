<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('system_langs', function (Blueprint $table) {
            $table->increments('id')->unique();
            $table->string('lang_key')->nullable();
            $table->string('english')->nullable();
            $table->string('german')->nullable();
            $table->string('arabic')->nullable();      
          });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('system_langs');
    }
};
