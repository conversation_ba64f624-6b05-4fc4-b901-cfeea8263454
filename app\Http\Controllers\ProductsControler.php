<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\mobile_products;

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;

class ProductsControler extends Controller
{
  
    public function ProductsView()
    {

        $packagesdata = mobile_products::orderBy('created_at', 'desc')->paginate(30);



        return view('products', ['products' => $packagesdata, 'searchcat' => '']);
    }



    public function StoreProduct(Request $request)
    {

        $user = Auth::user();
        $username = $user->id;
        $todatdate = date('Y-m-d');
        if ($request->cusappid) {

            $request->validate([
                'package_name' => ['required', 'max:255'],
                'package_type' => 'required',

            ]);


            $currectlandline = mobile_products::find($request->cusappid);
            $currectlandline->name = $request->name;
            $currectlandline->package_type = $request->package_type;
            $currectlandline->package_name  = $request->package_name;
            $currectlandline->num  = $request->num;
            $currectlandline->save();

        } else {

            $request->validate([
                'package_name' => ['required', 'max:255'],
                'package_type' => 'required',

            ]);




            $currectlandline = new mobile_products;
            $currectlandline->name = $request->name;
            $currectlandline->package_type = $request->package_type;
            $currectlandline->package_name  = $request->package_name;
            $currectlandline->num  = $request->num;


            $currectlandline->save();
        }


        return redirect()->back()->with('success', 'Your action was successful!');
    }


}
