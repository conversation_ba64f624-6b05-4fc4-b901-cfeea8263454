<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class alerts extends Model
{
    protected $fillable = [
        'event_date',
        'type',
        'comment',
        'user_id',
        'status',
        'location_id',

        // add other fields if needed later
    ];


    public function users()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }


    public function locvalues()
    {
        return $this->belongsTo(locations::class, 'location_id', 'id');
    }
}
