@extends('layouts.master_admin')
@section('admincontent')
    <!--begin::Main-->
    <div class="app-main flex-column flex-row-fluid" id="kt_app_main">
        <!--begin::Content wrapper-->
        <div class="d-flex flex-column flex-column-fluid">
            <!--begin::Content-->
            <div id="kt_app_content" class="app-content">
                <!--begin::Row-->
                <div class="row gy-5 g-xl-10">
                    <!--begin::Col-->
                    <div class="col-xl-12 mb-5 mb-xl-10">
                        <!--begin::Chart widget 32-->
                        <div class="card card-flush h-xl-100">
                            <!--begin::Header-->
                            <div class="card-header pt-5">
                                <!--begin::Title-->
                                <div class="card-title d-flex flex-column">
                                    <h2 class="fw-bold text-dark">{{ $groupByDisplayName }} Achievement Report</h2>
                                    <span class="text-muted fw-semibold fs-6">{{ $dateRange }}</span>
                                </div>
                                <!--end::Title-->
                                <!--begin::Toolbar-->
                                <div class="card-toolbar">
                                    <button type="button" class="btn btn-sm btn-icon btn-color-primary btn-active-light-primary" data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end">
                                        <i class="ki-duotone ki-filter fs-2">
                                            <span class="path1"></span>
                                            <span class="path2"></span>
                                        </i>
                                    </button>
                                    <!--begin::Menu-->
                                    <div class="menu menu-sub menu-sub-dropdown w-250px w-md-300px" data-kt-menu="true">
                                        <div class="px-7 py-5">
                                            <div class="fs-5 text-dark fw-bold">Filter Options</div>
                                        </div>
                                        <div class="separator border-gray-200"></div>
                                        <form action="{{ route('areareportfilter') }}" method="POST">
                                            @csrf
                                            <div class="px-7 py-5">
                                                <div class="mb-10">
                                                    <label class="form-label fw-semibold">Group By:</label>
                                                    <select class="form-select form-select-solid" name="group_by">
                                                        <option value="area_name" {{ $groupBy == 'area_name' ? 'selected' : '' }}>Area Name</option>
                                                        <option value="area" {{ $groupBy == 'area' ? 'selected' : '' }}>Area</option>
                                                        <option value="sector_name" {{ $groupBy == 'sector_name' ? 'selected' : '' }}>Sector Name</option>
                                                    </select>
                                                </div>
                                                <div class="mb-10">
                                                    <label class="form-label fw-semibold">Date From:</label>
                                                    <input type="date" class="form-control form-control-solid" name="date_from" value="{{ $startDate }}" />
                                                </div>
                                                <div class="mb-10">
                                                    <label class="form-label fw-semibold">Date To:</label>
                                                    <input type="date" class="form-control form-control-solid" name="date_to" value="{{ $endDate }}" />
                                                </div>
                                                <div class="d-flex justify-content-end">
                                                    <button type="reset" class="btn btn-sm btn-light btn-active-light-primary me-2">Reset</button>
                                                    <button type="submit" class="btn btn-sm btn-primary">Apply</button>
                                                </div>
                                            </div>
                                        </form>
                                    </div>
                                    <!--end::Menu-->
                                </div>
                                <!--end::Toolbar-->
                            </div>
                            <!--end::Header-->
                            <!--begin::Body-->
                            <div class="card-body pt-5">
                                <!--begin::Table-->
                                <div class="table-responsive">
                                    <table class="table table-row-dashed table-row-gray-300 align-middle gs-0 gy-4">
                                        <!--begin::Table head-->
                                        <thead>
                                            <tr class="fw-bold text-muted">
                                                <th class="min-w-150px">{{ $groupByDisplayName }}</th>
                                                <th class="min-w-200px">Locations</th>
                                                <th class="min-w-100px text-center">Mobile</th>
                                                <th class="min-w-100px text-center">ADSL</th>
                                                <th class="min-w-100px text-center">Landline</th>
                                                <th class="min-w-100px text-center">Total Services</th>
                                                <th class="min-w-120px text-center">Devices Sum</th>
                                            </tr>
                                        </thead>
                                        <!--end::Table head-->
                                        <!--begin::Table body-->
                                        <tbody>
                                            @forelse($results as $result)
                                                <tr>
                                                    <td>
                                                        <div class="d-flex align-items-center">
                                                            <div class="symbol symbol-45px me-5">
                                                                <div class="symbol-label bg-light-primary text-primary fw-bold">
                                                                    {{ strtoupper(substr($result['area_name'], 0, 2)) }}
                                                                </div>
                                                            </div>
                                                            <div class="d-flex justify-content-start flex-column">
                                                                <span class="text-dark fw-bold text-hover-primary fs-6">{{ $result['area_name'] }}</span>
                                                                <span class="text-muted fw-semibold text-muted d-block fs-7">{{ $result['location_count'] }} locations</span>
                                                            </div>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <div class="d-flex flex-column">
                                                            @foreach($result['locations'] as $location)
                                                                <span class="badge badge-light-info fs-8 mb-1">{{ $location }}</span>
                                                            @endforeach
                                                        </div>
                                                    </td>
                                                    <td class="text-center">
                                                        <span class="badge badge-light-success fs-7 fw-bold">{{ number_format($result['mobile_count']) }}</span>
                                                    </td>
                                                    <td class="text-center">
                                                        <span class="badge badge-light-primary fs-7 fw-bold">{{ number_format($result['adsl_count']) }}</span>
                                                    </td>
                                                    <td class="text-center">
                                                        <span class="badge badge-light-warning fs-7 fw-bold">{{ number_format($result['landline_count']) }}</span>
                                                    </td>
                                                    <td class="text-center">
                                                        <span class="badge badge-light-dark fs-6 fw-bold">{{ number_format($result['total_services']) }}</span>
                                                    </td>
                                                    <td class="text-center">
                                                        <span class="text-dark fw-bold fs-6">${{ number_format($result['devices_sum'], 2) }}</span>
                                                    </td>
                                                </tr>
                                            @empty
                                                <tr>
                                                    <td colspan="7" class="text-center py-10">
                                                        <div class="d-flex flex-column align-items-center">
                                                            <i class="ki-duotone ki-file-deleted fs-3x text-muted mb-4">
                                                                <span class="path1"></span>
                                                                <span class="path2"></span>
                                                            </i>
                                                            <span class="text-muted fs-6">No data found for the selected date range</span>
                                                        </div>
                                                    </td>
                                                </tr>
                                            @endforelse
                                        </tbody>
                                        <!--end::Table body-->
                                        <!--begin::Table footer-->
                                        <tfoot>
                                            <tr class="fw-bold text-gray-800 fs-6 border-top border-gray-200">
                                                <td class="text-dark">
                                                    <div class="d-flex align-items-center">
                                                        <div class="symbol symbol-45px me-5">
                                                            <div class="symbol-label bg-light-success text-success fw-bold">
                                                                <i class="ki-duotone ki-chart-simple fs-2">
                                                                    <span class="path1"></span>
                                                                    <span class="path2"></span>
                                                                    <span class="path3"></span>
                                                                    <span class="path4"></span>
                                                                </i>
                                                            </div>
                                                        </div>
                                                        <div class="d-flex justify-content-start flex-column">
                                                            <span class="text-dark fw-bold fs-6">TOTAL</span>
                                                            <span class="text-muted fw-semibold d-block fs-7">{{ $totals['area_count'] }} {{ strtolower($groupByDisplayName) }}s</span>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>
                                                    <span class="badge badge-light-dark fs-8">All Locations</span>
                                                </td>
                                                <td class="text-center">
                                                    <span class="badge badge-success fs-7 fw-bold">{{ number_format($totals['mobile_count']) }}</span>
                                                </td>
                                                <td class="text-center">
                                                    <span class="badge badge-primary fs-7 fw-bold">{{ number_format($totals['adsl_count']) }}</span>
                                                </td>
                                                <td class="text-center">
                                                    <span class="badge badge-warning fs-7 fw-bold">{{ number_format($totals['landline_count']) }}</span>
                                                </td>
                                                <td class="text-center">
                                                    <span class="badge badge-dark fs-6 fw-bold">{{ number_format($totals['total_services']) }}</span>
                                                </td>
                                                <td class="text-center">
                                                    <span class="text-dark fw-bold fs-6">${{ number_format($totals['devices_sum'], 2) }}</span>
                                                </td>
                                            </tr>
                                        </tfoot>
                                        <!--end::Table footer-->
                                    </table>
                                </div>
                                <!--end::Table-->
                            </div>
                            <!--end::Body-->
                        </div>
                        <!--end::Chart widget 32-->
                    </div>
                    <!--end::Col-->
                </div>
                <!--end::Row-->

                <!--begin::Summary Cards Row-->
                <div class="row g-5 g-xl-10 mb-5 mb-xl-10">
                    <!--begin::Col-->
                    <div class="col-xl-3">
                        <!--begin::Statistics Widget 5-->
                        <div class="card bg-body hoverable card-xl-stretch mb-xl-8">
                            <div class="card-body">
                                <i class="ki-duotone ki-devices-2 text-primary fs-2x ms-n1">
                                    <span class="path1"></span>
                                    <span class="path2"></span>
                                    <span class="path3"></span>
                                </i>
                                <div class="text-gray-900 fw-bold fs-2 mb-2 mt-5">
                                    {{ number_format($totals['mobile_count']) }}
                                </div>
                                <div class="fw-semibold text-gray-400">Total Mobile Services</div>
                            </div>
                        </div>
                        <!--end::Statistics Widget 5-->
                    </div>
                    <!--end::Col-->
                    <!--begin::Col-->
                    <div class="col-xl-3">
                        <!--begin::Statistics Widget 5-->
                        <div class="card bg-body hoverable card-xl-stretch mb-xl-8">
                            <div class="card-body">
                                <i class="ki-duotone ki-wifi text-success fs-2x ms-n1">
                                    <span class="path1"></span>
                                    <span class="path2"></span>
                                    <span class="path3"></span>
                                </i>
                                <div class="text-gray-900 fw-bold fs-2 mb-2 mt-5">
                                    {{ number_format($totals['adsl_count']) }}
                                </div>
                                <div class="fw-semibold text-gray-400">Total ADSL Services</div>
                            </div>
                        </div>
                        <!--end::Statistics Widget 5-->
                    </div>
                    <!--end::Col-->
                    <!--begin::Col-->
                    <div class="col-xl-3">
                        <!--begin::Statistics Widget 5-->
                        <div class="card bg-body hoverable card-xl-stretch mb-xl-8">
                            <div class="card-body">
                                <i class="ki-duotone ki-call text-warning fs-2x ms-n1">
                                    <span class="path1"></span>
                                    <span class="path2"></span>
                                </i>
                                <div class="text-gray-900 fw-bold fs-2 mb-2 mt-5">
                                    {{ number_format($totals['landline_count']) }}
                                </div>
                                <div class="fw-semibold text-gray-400">Total Landline Services</div>
                            </div>
                        </div>
                        <!--end::Statistics Widget 5-->
                    </div>
                    <!--end::Col-->
                    <!--begin::Col-->
                    <div class="col-xl-3">
                        <!--begin::Statistics Widget 5-->
                        <div class="card bg-body hoverable card-xl-stretch mb-xl-8">
                            <div class="card-body">
                                <i class="ki-duotone ki-dollar text-info fs-2x ms-n1">
                                    <span class="path1"></span>
                                    <span class="path2"></span>
                                    <span class="path3"></span>
                                </i>
                                <div class="text-gray-900 fw-bold fs-2 mb-2 mt-5">
                                    ${{ number_format($totals['devices_sum'], 2) }}
                                </div>
                                <div class="fw-semibold text-gray-400">Total Devices Revenue</div>
                            </div>
                        </div>
                        <!--end::Statistics Widget 5-->
                    </div>
                    <!--end::Col-->
                </div>
                <!--end::Summary Cards Row-->
            </div>
            <!--end::Content-->
        </div>
        <!--end::Content wrapper-->
    </div>
    <!--end::Main-->
@endsection
