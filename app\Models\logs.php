<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class logs extends Model
{
    protected $fillable = [
        'date',
        'day',
        'shift',
        'signin',
        'signout',
        'nickname',
        'user_id',
        'ip',
        'eval',
        'permission',
        'comment',
        'location_id',
        'location',
        'username',
        'comment2',
        // add other fields if needed later
    ];


    public function users()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }


    public function locvalues()
    {
        return $this->belongsTo(locations::class, 'location_id', 'id');
    }



}
