<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Session;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
           // Ensure the table exists to avoid errors during migration
    if (Schema::hasTable('app_details')) {
        $timezone = DB::table('app_details')
        ->where('section', 'Store TimeZone')
        ->value('note') ?? config('app.timezone');

        date_default_timezone_set($timezone);
        Config::set('app.timezone', $timezone);
    }
    


    // if (Session::has('locale')) {
    //     App::setLocale(Session::get('locale'));
    // }


    }
}
