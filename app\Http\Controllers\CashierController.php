<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use App\Models\cashier_report;
use App\Models\fraction;
use App\Models\User;
use Carbon\Carbon;
use App\Models\locations;

class CashierController extends Controller
{

    public function CashierReportView()
    {

        $summary = cashier_report::with('location')
            ->select(
                'cashbox_date',
                'location_id',
                DB::raw('SUM(total_cash) as total_cash'),
                DB::raw('SUM(total_visa) as total_visa'),
                DB::raw('SUM(total) as total'),
                DB::raw('SUM(bss_cash) as bss_cash'),
                DB::raw('SUM(bss_visa) as bss_visa'),
                DB::raw('SUM(oss_cash) as oss_cash'),
                DB::raw('SUM(oss_visa) as oss_visa'),
                DB::raw('SUM(delta_cash) as delta_cash'),
                DB::raw('SUM(delta_visa) as delta_visa'),
                DB::raw('SUM(telegraph_cash) as telegraph_cash'),
                DB::raw('SUM(telegraph_visa) as telegraph_visa'),
                DB::raw('SUM(wallet_in) as wallet_in'),
                DB::raw('SUM(wallet_out) as wallet_out'),
                DB::raw('SUM(e_payment) as e_payment'),
                DB::raw('SUM(vat) as vat'),
                DB::raw('SUM(tax) as tax'),
                DB::raw('SUM(chique) as chique')
            )
            ->groupBy('cashbox_date', 'location_id')
            ->orderBy('cashbox_date', 'desc')
            ->get();

        // Get detailed cashier records grouped by date
        $details = DB::table('cashier_reports')
            ->select('cashbox_date', 'user_id', 'bss_cash', 'bss_visa', 'oss_cash', 'oss_visa', 'delta_cash', 'delta_visa', 'telegraph_cash', 'telegraph_visa', 'wallet_in', 'wallet_out', 'e_payment', 'vat', 'tax', 'chique', 'total_cash', 'total_visa', 'id', 'location_id')
            ->orderBy('cashbox_date', 'desc')
            ->get()
            ->groupBy('cashbox_date');

        return view('/cashier/cashierIiesview', compact('summary', 'details'));
    }


    public function FractionView()
    {

        // $packagesdata = fraction::orderBy('created_at', 'desc')->paginate(30);



        if (in_array(Auth::user()->role_name, ['Agent', 'Leader', 'Senior', 'Supervisor'])) {

            $query = fraction::where('location_id', Auth::user()->location_id);

            if (in_array(Auth::user()->role_name, ['Agent', 'Leader'])) {

                $query->where('user_id', Auth::user()->id);
            }

            $packagesdata = $query->orderBy('created_at', 'desc')->get();
        } else {
            $packagesdata = fraction::orderBy('created_at', 'desc')->get();
        }





        return view('/cashier/fractionview', ['fractions' => $packagesdata, 'searchcat' => '']);
    }


    public function CashierView()
    {

        $searchcat = '';


        if (in_array(Auth::user()->role_name, ['Agent', 'Leader', 'Senior', 'Supervisor'])) {

            $query = cashier_report::where('location_id', Auth::user()->location_id);

            if (in_array(Auth::user()->role_name, ['Agent', 'Leader'])) {

                $query->where('user_id', Auth::user()->id);
            }

            $cashierReports = $query->orderBy('created_at', 'desc')->paginate(30);
        } else {
            $cashierReports = cashier_report::orderBy('created_at', 'desc')->paginate(30);
        }

        $locations     = locations::all();
        $cashierusers = User::whereNot('role_name', 'Admins')->where('location_id', '1')->get();
        return view('cashier.cashierview', compact('cashierReports', 'searchcat', 'locations', 'cashierusers'));
    }




    public function CashierReport()
    {

        $todaydate = date('Y-m-d');
        $user = Auth::user();
        $locationca = $location = $user->location_id;


        $cashierReports = cashier_report::where('cashbox_date', $todaydate)->where('location_id', $locationca)->orderBy('cashbox_date', 'desc')->get();
        $searchcat = '';


        $locations     = locations::all();

        $cashTotal = cashier_report::where('cashbox_date', $todaydate)
            ->where('location_id', $location)
            ->sum('total_cash');

        $visaTotal = cashier_report::where('cashbox_date', $todaydate)
            ->where('location_id', $location)
            ->sum('total_visa');

        $cashierfractions = fraction::where('cashbox_date', $todaydate)->where('location_id', $location)->orderBy('cashbox_date', 'desc')->get();


        return view('/cashier/cashier_report', compact('cashierReports', 'searchcat', 'todaydate', 'locations', 'location', 'cashTotal', 'visaTotal', 'cashierfractions'));
    }


    public function CashierReportFilter(Request $request)
    {


        $user = Auth::user();

        $todaydate = $request->cashdate;
        $location = $request->location;



        $cashierReports = cashier_report::where('cashbox_date', $todaydate)->where('location_id', $location)->orderBy('cashbox_date', 'desc')->get();
        $searchcat = '';


        $locations     = locations::all();

        $cashTotal = cashier_report::where('cashbox_date', $todaydate)
            ->where('location_id', $location)
            ->sum('total_cash');

        $visaTotal = cashier_report::where('cashbox_date', $todaydate)
            ->where('location_id', $location)
            ->sum('total_visa');

        $cashierfractions = fraction::where('cashbox_date', $todaydate)->where('location_id', $location)->orderBy('cashbox_date', 'desc')->get();


        return view('/cashier/cashier_report', compact('cashierReports', 'searchcat', 'todaydate', 'locations', 'location', 'cashTotal', 'visaTotal', 'cashierfractions'));
    }


    public function CashierReportChange(Request $request)
    {


        $todaydate = date('Y-m-d');
        $user = Auth::user();
        $locationca = $user->location_id;

        if ($request->cashierdate) {
            $cashierdate = $request->cashierdate;
        } else {
            $cashierdate = $todaydate;
        }
        if ($request->location) {
            $cashierlocation = $request->location;
        } else {
            $cashierlocation = $locationca;
        }

        $cashierReports = cashier_report::where('cashbox_date', $cashierdate)->where('location_id', $cashierlocation)->orderBy('cashbox_date', 'desc')->get;
        $searchcat = '';
        return view('/cashier/cashier_report', compact('cashierReports', 'searchcat'));
    }



    public function NewFraction()
    {

        $todaudate = date('Y-m-d');
        $user = Auth::user();
        $totalCash = DB::table('cashier_reports')
            ->whereDate('cashbox_date', $todaudate)
            ->where('user_id', $user->id)
            ->value('total_cash') ?? 0;



        $fraction = DB::table('fractions')
            ->whereDate('cashbox_date', $todaudate)
            ->where('user_id', $user->id)
            ->first() ?? (object)[
                'user_id' => $user->id, // Add this if needed
                'id' => null,
                'C_200' => 0, // set defaults if needed
                'C_100' => 0, // set defaults if needed
                'C_50' => 0, // set defaults if needed
                'C_20' => 0, // set defaults if needed
                'C_10' => 0, // set defaults if needed
                'C_5' => 0, // set defaults if needed
                'C_1' => 0, // set defaults if needed
                'C_0_5' => 0, // set defaults if needed
                'C_0_25' => 0, // set defaults if needed
                'total' => 0, // set defaults if needed
                'amount' => 0, // set defaults if needed
                'short' => 0, // set defaults if needed
                'description' => null, // set defaults if needed
            ];


        $cashierusers = User::whereNot('role_name', 'Admins')->where('location_id', $user->location_id)->get();


        return view('/cashier/fraction_new', compact('totalCash', 'todaudate', 'fraction', 'user', 'cashierusers'));
    }




    public function GetCashierFraction(Request $request)
    {

        $todaudate   = $request->cashierdate;
        $cashieruser = $request->cashieruser;
        $totalCash   = DB::table('cashier_reports')
            ->whereDate('cashbox_date', $todaudate)
            ->where('user_id', $cashieruser)
            ->value('total_cash') ?? 0;


        $user   = User::find($cashieruser);


        $cashierusers = User::whereNot('role_name', 'Admins')->where('location_id',  $user->location_id)->get();


        $fraction = DB::table('fractions')
            ->whereDate('cashbox_date', $todaudate)
            ->where('user_id', $cashieruser)
            ->first() ?? (object)[
                'user_id' => $cashieruser, // set defaults if needed
                'C_200' => 0, // set defaults if needed
                'C_100' => 0, // set defaults if needed
                'C_50' => 0, // set defaults if needed
                'C_20' => 0, // set defaults if needed
                'C_10' => 0, // set defaults if needed
                'C_5' => 0, // set defaults if needed
                'C_1' => 0, // set defaults if needed
                'C_0_5' => 0, // set defaults if needed
                'C_0_25' => 0, // set defaults if needed
                'total' => 0, // set defaults if needed
                'amount' => 0, // set defaults if needed
                'short' => 0, // set defaults if needed
                'id' => null, // set defaults if needed
                'description' => null, // set defaults if needed
            ];

        return view('/cashier/fraction_new', compact('totalCash', 'todaudate', 'fraction', 'user', 'cashierusers'));
    }

    public function SearchCashier(Request $request)
    {


        $search = $searchcat = $request->searchval;

        // Try to get matching location or user IDs
        $locationId = locations::where('name_ar', 'like', "%{$search}%")
            ->orWhere('name_en', 'like', "%{$search}%")
            ->value('id');

        $usernameId = User::where('name', 'like', "%{$search}%")
            ->orWhere('username', 'like', "%{$search}%")
            ->orWhere('emp_num', 'like', "%{$search}%")
            ->orWhere('ar_name', 'like', "%{$search}%")
            ->value('id');

        $user = Auth::user();

        // Start base query
        $query = cashier_report::query();

        if (in_array($user->role_name, ['Agent', 'Leader', 'Senior', 'Supervisor'])) {
            // Limit to user's location
            $query->where(function ($q) use ($search, $usernameId, $locationId, $user) {
                $q->where('location_id', $user->location_id)
                    ->where(function ($sub) use ($search, $usernameId, $locationId) {
                        $sub->where('cashbox_date', 'like', "%{$search}%")
                            ->orWhere('user_id', $usernameId)
                            ->orWhere('location_id', $locationId);
                    });
            });

            // For Agent and Leader: also restrict by user_id
            if (in_array($user->role_name, ['Agent', 'Leader'])) {
                $query->where('user_id', $user->user_id);
            }
        } else {
            // Admin or other roles: no location/user restrictions
            $query->where(function ($q) use ($search, $usernameId, $locationId) {
                $q->where('cashbox_date', 'like', "%{$search}%")
                    ->orWhere('user_id', $usernameId)
                    ->orWhere('location_id', $locationId);
            });
        }

        $cashierReports = $query->orderBy('created_at', 'desc')->paginate(30);

        $locations     = locations::all();
        $cashierusers = User::whereNot('role_name', 'Admins')->where('location_id', '1')->get();

        return view('cashier.cashierview', compact('cashierReports', 'searchcat', 'locations', 'cashierusers'));
    }



    public function FilterCashier(Request $request)
    {

        $datefrom    = $request->fildatefrom;
        $dateto      = $request->fildateto;
        $usernameId  = $request->filteruser;
        $locationId  = $request->filterlocation;

        $searchcat = ''; // if used in view

        $user = Auth::user();
        $query = cashier_report::query();

        // Role-based filtering
        if (in_array($user->role_name, ['Agent', 'Leader', 'Senior', 'Supervisor'])) {
            $query->where('location_id', $user->location_id);

            if (in_array($user->role_name, ['Agent', 'Leader'])) {
                $query->where('user_id', $user->user_id);
            }
        }

        // Global filters
        if ($datefrom && $dateto) {
            $query->whereBetween('cashbox_date', [$datefrom, $dateto]);
        }

        if ($usernameId) {
            $query->where('user_id', $usernameId);
        }

        if ($locationId) {
            $query->where('location_id', $locationId);
        }

        $cashierReports = $query->orderBy('created_at', 'desc')->paginate(30);

        $locations     = locations::all();
        $cashierusers = User::whereNot('role_name', 'Admins')->where('location_id', '1')->get();


        return view('cashier.cashierview', compact('cashierReports', 'searchcat', 'locations', 'cashierusers', 'datefrom', 'dateto', 'usernameId', 'locationId'));
    }



    public function AddCashier()
    {

        return view('cashier/addcashier', ['total_cash' => '0']);
    }


    public function StoreCashier(Request $request)
    {

        $user = Auth::user();
        $username = $user->email;
        $datetoday = date('Y-m-d');

        if ($request->catid) {

            $request->validate([
                'total_cash' => ['required', 'max:255'],
                'total_visa' => 'required',

            ]);

            $newcashier_report = cashier_report::find($request->catid);
            $newcashier_report->bss_cash = $request->bss_cash;
            $newcashier_report->bss_visa = $request->bss_visa;
            $newcashier_report->oss_cash = $request->oss_cash;
            $newcashier_report->oss_visa = $request->oss_visa;
            $newcashier_report->wallet_in = $request->wallet_in;
            $newcashier_report->wallet_out = $request->wallet_out;
            $newcashier_report->e_payment = $request->e_payment;
            $newcashier_report->vat = $request->vat;
            $newcashier_report->tax = $request->tax;
            $newcashier_report->chique = $request->chique;
            $newcashier_report->telegraph_cash = $request->telegraph_cash;
            $newcashier_report->telegraph_visa = $request->telegraph_visa;
            $newcashier_report->delta_cash = $request->delta_cash;
            $newcashier_report->delta_visa = $request->delta_visa;
            $newcashier_report->total_cash = $request->total_cash;
            $newcashier_report->total_visa = $request->total_visa;
            $newcashier_report->total = $request->total_cash + $request->total_visa;
            $newcashier_report->save();
        } else {

            $request->validate([
                'total_cash' => ['required', 'max:255'],
                'total_visa' => 'required',

            ]);

            $exists = cashier_report::where('cashbox_date', $datetoday)
                ->where('user_id', $user->id)
                ->exists();

            if ($exists) {

                cashier_report::where('cashbox_date', $datetoday)->where('user_id', $user->id)->delete();
            }
            $newcashier_report = new cashier_report;
            $newcashier_report->cashbox_date = $datetoday;

            $newcashier_report->bss_cash = $request->bss_cash;
            $newcashier_report->bss_visa = $request->bss_visa;

            $newcashier_report->oss_cash = $request->oss_cash;
            $newcashier_report->oss_visa = $request->oss_visa;

            $newcashier_report->wallet_in = $request->wallet_in;
            $newcashier_report->wallet_out = $request->wallet_out;

            $newcashier_report->e_payment = $request->e_payment;
            $newcashier_report->vat = $request->vat;
            $newcashier_report->tax = $request->tax;
            $newcashier_report->chique = $request->chique;

            $newcashier_report->telegraph_cash = $request->telegraph_cash;
            $newcashier_report->telegraph_visa = $request->telegraph_visa;
            $newcashier_report->delta_cash = $request->delta_cash;
            $newcashier_report->delta_visa = $request->delta_visa;

            $newcashier_report->total_cash = $request->total_cash;
            $newcashier_report->total_visa = $request->total_visa;
            $newcashier_report->total = $request->total_cash + $request->total_visa;


            $newcashier_report->user_id = $user->id;
            $newcashier_report->location_id = $user->location_id;

            $newcashier_report->save();
            $newId = $newcashier_report->id; // Get the inserted ID




        }
        $cashierusers = User::wherenot('role_name', 'Admins')->where('location_id',  $user->location_id)->get();


        $today = date('Y-m-d');
        $totalCash = DB::table('cashier_reports')
            ->whereDate('cashbox_date', $today)
            ->where('user_id', $user->id)
            ->value('total_cash') ?? 0;

        // return view('/cashier/fraction_new', compact('totalCash', 'today','cashierusers'));


        return redirect('/cashier/fraction_new');
    }


    public function EditCashier($memid)
    {

        if ($memid) {


            $cashierreport   = cashier_report::find($memid);
            $locations     = locations::all();

            return view('/cashier/editcashier', ['locations' => $locations, 'cashierreport' => $cashierreport]);
        } else {
        }
    }


    public function DeleteCashier($catid = null)
    {

        if ($catid) {

            $currectcategory = cashier_report::find($catid);
            $currectcategory->delete();
        } else {

            abort(403, 'Unauthorized action.');
        }

        return redirect()->route('cashierview')->with('success', 'Cashier Report Deleted Successfully');
    }


    public function StoreFraction(Request $request)
    {

        $user = Auth::user();
        $username = $user->email;
        $datetoday = $todaudate = date('Y-m-d');


            // $exists = fraction::where('cashbox_date', $datetoday)
            //     ->where('user_id', $user->id)
            //     ->exists();

            // if ($exists) {

            //     fraction::where('cashbox_date', $datetoday)->where('user_id', $user->id)->delete();
            // }

        if ($request->cashier_id) {

            // $request->validate([
            //     'category' => ['required', 'max:255'],
            //     'discribtion' => 'required',
            //     'catimage' => 'image|mimes:jpeg,png,jpg,gif,svg|max:2048',

            // ]);


            $currectfraction = fraction::find($request->cashier_id);
            $currectfraction->C_200 = $request->cv_200;
            $currectfraction->C_100 = $request->cv_100;

            $currectfraction->C_50 = $request->cv_50;
            $currectfraction->C_20 = $request->cv_20;

            $currectfraction->C_10 = $request->cv_10;
            $currectfraction->C_5 = $request->cv_5;

            $currectfraction->C_1 = $request->cv_1;
            $currectfraction->C_0_5 = $request->cv_05;
            $currectfraction->C_0_25 = $request->cv_025;

            $currectfraction->total = $request->totalcash;
            $currectfraction->amount = $request->totalamount;
            $currectfraction->short = $request->totalshort;
            $currectfraction->description = $request->description;

            $currectfraction->save();
        } else {

            

            // $request->validate([
            //     'mob_sku' => ['required', 'max:255'],
            //     'mob_number' => 'required',

            // ]);









            $newcfraction = new fraction;
            $newcfraction->cashbox_date = $datetoday;

            $newcfraction->C_200 = $request->cv_200;
            $newcfraction->C_100 = $request->cv_100;

            $newcfraction->C_50 = $request->cv_50;
            $newcfraction->C_20 = $request->cv_20;

            $newcfraction->C_10 = $request->cv_10;
            $newcfraction->C_5 = $request->cv_5;

            $newcfraction->C_1 = $request->cv_1;
            $newcfraction->C_0_5 = $request->cv_05;
            $newcfraction->C_0_25 = $request->cv_025;

            $newcfraction->total = $request->totalcash;
            $newcfraction->amount =  str_replace(',', '', $request->totalamount);
            $newcfraction->short = $request->totalshort;
            $newcfraction->description = $request->description;

            $newcfraction->user_id = $user->id;
            $newcfraction->location_id = $user->location_id;

            $newcfraction->save();
            $newId = $newcfraction->id; // Get the inserted ID




        }




        
        // $cashierusers = User::where('role_name', 'Admins')->where('location_id', '1')->get();

        // $fraction = DB::table('fractions')
        //     ->whereDate('cashbox_date', $datetoday)
        //     ->where('user_id', $user->id)
        //     ->first() ?? (object)[
        //         'C_200' => 0, // set defaults if needed
        //         'C_100' => 0, // set defaults if needed
        //         'C_50' => 0, // set defaults if needed
        //         'C_20' => 0, // set defaults if needed
        //         'C_10' => 0, // set defaults if needed
        //         'C_5' => 0, // set defaults if needed
        //         'C_1' => 0, // set defaults if needed
        //         'C_0_5' => 0, // set defaults if needed
        //         'C_0_25' => 0, // set defaults if needed
        //         'total' => 0, // set defaults if needed
        //         'amount' => 0, // set defaults if needed
        //         'short' => 0, // set defaults if needed
        //         'id' => null, // set defaults if needed
        //         'description' => null, // set defaults if needed
        //     ];


        // $today = date('Y-m-d');
        // $totalCash = DB::table('cashier_reports')
        //     ->whereDate('cashbox_date', $today)
        //     ->where('user_id', $user->id)
        //     ->value('total_cash') ?? 0;

        // return view('/cashier/fraction_new', compact('totalCash', 'today', 'cashierusers', 'user', 'fraction', 'todaudate'))->with('success', 'Cashier Report Deleted Successfully');


        return redirect()->route('newfraction')->with('success', 'Cashier Report Deleted Successfully');

    }




    public function PrintCashier(Request $request)
    {
        $date = $request->input('dateid');
        $userId = $request->input('user');


        // $date = '2025-04-29';
        // $userId = 1; 

            // $existscasier = cashier_report::where('cashbox_date', $date)
            //     ->where('user_id', $userId)
            //     ->exists();

            // if (!$existscasier) {

            // return redirect()->back()->with('error', 'No report found for selected date and user.');
            // }



        // $user = User::where('memberID', $userId)->first();
        $report = cashier_report::where([
            ['user_id', $userId],
            ['cashbox_date', $date],
        ])->first();

        $data = [
            'user' => $userId,
            'report' => $report,
            'fulldate' => now()->format('d F Y, h:i:s A'),

        ];

        if (!$report) {
            return redirect()->back()->with('error', 'No report found for selected date and user.');
        }


        return view('cashier.cashierprint', $data);
    }
}
