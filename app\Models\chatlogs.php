<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class chatlogs extends Model
{

    protected $fillable = [
        'chat_date',
        'chat_time',
        'chat_message',
        'sender_id',
        'receiver_id',
        'upload',
        'status',
        // add other fields if needed later
    ];


    public function sender()
    {
        return $this->belongsTo(User::class, 'sender_id', 'id');
    }



    public function receiver()
    {
        return $this->belongsTo(User::class, 'receiver_id', 'id');
    }


    
}
