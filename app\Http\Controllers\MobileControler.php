<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

use App\Models\mobile_packages;
use App\Models\mobile_products;
use App\Models\mobile;

use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;


class MobileControler extends Controller
{
    public function NewMobile()
    {
        $allpackages = mobile_packages::whereIn('category', ['Mobile', 'Wallet', 'SWAP'])->get();


        return view('mobile/addmobile', ['packages' => $allpackages]);
    }
    public function MobileView()
    {


        if (in_array(Auth::user()->role_name, ['Agent', 'Leader', 'Senior', 'Supervisor'])) {

            $query = mobile::where('location_id', Auth::user()->location_id);



            if (in_array(Auth::user()->role_name, ['Agent', 'Leader'])) {

                $query->where('user_id', Auth::user()->id);
            }

            $allpackages = $query->orderBy('cashbox_date', 'desc')->paginate(30);


        } else {

            $allpackages = mobile::orderBy('cashbox_date', 'desc')->paginate(30);
        }




        return view('mobile/mobileview', ['contratcs' => $allpackages, 'searchcat' => '']);
    }

    public function StoreMobile(Request $request)
    {

        $user = Auth::user();
        $username = $user->email;

        if ($request->catid) {

            // $request->validate([
            //     'category' => ['required', 'max:255'],
            //     'discribtion' => 'required',
            //     'catimage' => 'image|mimes:jpeg,png,jpg,gif,svg|max:2048',

            // ]);

            // $categorylink   = category::find($request->pro_family);



            // $currectcategory = product::find($request->catid);
            // $currectcategory->category_id = $categorylink->id;
            // $currectcategory->sub_category = $categorylink->sub_category;
            // $currectcategory->family = $categorylink->family;
            // $currectcategory->product_name = $request->pro_name;
            // $currectcategory->product_code = $request->pro_code;
            // $currectcategory->product_sku = $request->product_sku;
            // $currectcategory->product_type = $request->pro_type;
            // $currectcategory->brand = $request->pro_brand;
            // $currectcategory->size = $request->pro_size;
            // $currectcategory->color = $request->pro_color;
            // $currectcategory->ord_qty = $request->pro_max_qty;
            // $currectcategory->stock_qty = $request->pro_quantity;
            // $currectcategory->price = $request->pro_price;
            // $currectcategory->old_price = $request->pro_olprice;
            // $currectcategory->offer = $request->pro_offer;
            // $currectcategory->username = $username;
            // $currectcategory->status  = $request->cusstatus;
            // $currectcategory->discribtion = $request->description;


            // $currectcategory->save();

        } else {


            $validatnumber = mobile::where('number', $request->mob_number)->where('cashbox_date', date('Y-m-d'))->exists();

            if ($validatnumber) {
                return redirect()->route('newmobile')->with('error', 'This Number Already Exist');
            }
            $user = Auth::user();



            $mobilepackge = mobile_packages::find($request->mob_package);


            $newmobile = new mobile;
            $newmobile->cashbox_date = date('Y-m-d');
            $newmobile->product_sku = $request->mob_sku;
            $newmobile->number = $request->mob_number;
            $newmobile->comment = $request->description;
            $newmobile->addons = $request->mob_addon;
            $newmobile->package_code = $request->mob_package;
            $newmobile->product_code = 1;

            $newmobile->contract_type = $mobilepackge->package_type;


            $newmobile->user_id = $user->id;
            $newmobile->location_id = $user->location_id;

            $newmobile->save();
            $newId = $newmobile->id; // Get the inserted ID

        }

        return redirect()->route('newmobile')->with('success', 'Cashier Report Deleted Successfully');
    }


    public function EditMobile($catid = null)
    {

        $currectcategory = mobile::findorfail($catid);

        return view('admin/categories/editcategory', ['categorydata' => $currectcategory]);
    }




    public function ViewCategory()
    {

        $catresultb = mobile::orderBy('created_at', 'desc')->paginate(10);


        $categoryCount = mobile::count();

        return view('admin/categories/categorIiesview', ['categories' => $catresultb, 'cat_count' => $categoryCount, 'searchcat' => '']);
    }

    public function DeleteMobile($catid = null)
    {

        if ($catid) {

            $currectcategory = mobile::find($catid);
            $currectcategory->delete();
        } else {

            abort(403, 'Unauthorized action.');
        }

        return redirect()->route('mobileview');
    }

    public function SearchMobile(Request $request)
    {


        $search = $request->searchcategory;

        $allpackages = mobile::where(function ($query) use ($search) {
            $query->where('product_sku', 'like', '%' . $search . '%')
                ->orWhere('number', 'like', '%' . $search . '%')
                ->orWhere('contract_type', 'like', '%' . $search . '%')
                ->orWhere('addons', 'like', '%' . $search . '%');
        })->orWhereHas('packages', function ($query) use ($search) {
            $query->where('package_name', 'like', '%' . $search . '%');
        })->orWhereHas('products', function ($query) use ($search) {
            $query->where('Product_name', 'like', '%' . $search . '%');
        })->orWhereHas('location', function ($query) use ($search) {
            $query->where('name_en', 'like', '%' . $search . '%');
        })->paginate(10);


        return view('mobile/mobileview', ['contratcs' => $allpackages, 'searchcat' => $request->searchcategory]);
    }
}
