<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cashboxes', function (Blueprint $table) {
            $table->id();
            $table->date('cashbox_date')->nullable();


            $table->string('prepaid_sales')->nullable();
            $table->string('data_mix_sales')->nullable();
            $table->string('postpaid_sales')->nullable();
            $table->string('adsl_sales')->nullable();
            $table->string('fixed_sales')->nullable();
            $table->string('total_receipts')->nullable();
            $table->string('transactions')->nullable();


            $table->float('device_amount', 1, 2)->nullable();
            $table->float('cashbox_amount', 1, 2)->nullable();

            $table->unsignedBigInteger('location_id')->nullable();
            $table->foreign('location_id')->references('id')->on('locations')->onDelete('cascade');
            $table->unsignedBigInteger('user_id')->nullable();
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');



            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cashboxes');
    }
};
