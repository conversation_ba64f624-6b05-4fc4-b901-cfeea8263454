<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('chatlogs', function (Blueprint $table) {
            $table->id();

            $table->date('chat_date')->nullable();
            $table->time('chat_time')->nullable();



            $table->string('chat_message')->nullable(); // e.g., Rotation 1, Rotation 2

            $table->unsignedBigInteger('sender_id')->nullable();
            $table->foreign('sender_id')->references('id')->on('users')->onDelete('cascade');
            $table->unsignedBigInteger('receiver_id')->nullable();
            $table->foreign('receiver_id')->references('id')->on('users')->onDelete('cascade');

            $table->string('upload')->nullable(); // e.g., Rotation 1, Rotation 2
            $table->string('status')->nullable(); // e.g., Rotation 1, Rotation 2

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('chatlogs');
    }
};
