<?php

namespace App\Imports;

use App\Models\User;
use App\Models\cashbox;
use App\Models\wallet_cashbox;
use App\Models\landline;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Carbon\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;



class Walletdata implements ToModel
{
    /**
     * @param array $row
     *
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public $totalCashin = 0;
    public $totalCashout = 0;
    public $totalCheque = 0;
    public $totaltax = 0;
    protected $importDate;
    
    public function model(array $row)
    {
        $user = Auth::user();
        $username = $user->id;
        $userlocation = $user->location;
    
        // Parse Excel Date to Carbon instance
        $tran_date = \PhpOffice\PhpSpreadsheet\Shared\Date::excelToDateTimeObject($row[0]);
        $tran_date = \Carbon\Carbon::instance($tran_date)->format('Y-m-d');
    
        $tran_id     = trim($row[1]);
        $tran_type   = trim($row[2]);
        $tran_mobil  = trim($row[3]);
        $tran_amount = floatval(trim($row[4]));  // Ensure the amount is a float
        $tran_reff   = trim($row[5]);
        $tran_user   = trim($row[6]);
    
        // Delete previous records for the user and the date
        if (!$this->importDate) {
            $this->importDate = $tran_date;
            wallet_cashbox::where('cashbox_date', $this->importDate)->where('user_id', $username)->delete();
        }
    
        // Only proceed if the transaction date is valid
        if (!empty($tran_date)) {
            // Update totals based on transaction type
            if ($tran_type == 'Cash In') {
                $this->totalCashin += $tran_amount;
            }
    
            if ($tran_type == 'Cash Out' || $tran_type == 'Refund Registration') {
                $cashout = abs($tran_amount); // Remove negative sign if exists
                $this->totalCashout += $cashout;
            }
    
            // Insert data only for specific transaction types
            if (in_array($tran_type, ['Cash In', 'Cash Out', 'Refund Registration'])) {
                wallet_cashbox::create([
                    'tran_id'      => $tran_id,
                    'tran_type'    => $tran_type,
                    'cashbox_date' => $tran_date,
                    'number'       => $tran_mobil,
                    'reference'    => $tran_reff,
                    'amount'       => $tran_amount,
                    'location_id'  => $userlocation,
                    'user_id'      => $username,
                ]);
            }
        }
    }
}
