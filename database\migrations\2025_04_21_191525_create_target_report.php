<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('target_report', function (Blueprint $table) {
            $table->id();
            $table->date('date'); // The exact day this schedule entry is for
            $table->string('work_value')->nullable(); // e.g., D, N, Off, H, etc.
            $table->string('package')->nullable(); // e.g., Rotation 1, Rotation 2
            $table->string('quantity')->nullable(); // e.g., Rotation 1, Rotation 2
            $table->string('category')->nullable(); // e.g., Rotation 1, Rotation 2
            $table->string('type')->nullable(); // e.g., Rotation 1, Rotation 2

            $table->string('location')->nullable(); // e.g., Rotation 1, Rotation 2
            $table->string('user_name')->nullable(); // e.g., Rotation 1, Rotation 2

            $table->unsignedBigInteger('package_id')->nullable();
            $table->foreign('package_id')->references('id')->on('mobile_packages')->onDelete('cascade');  

            $table->unsignedBigInteger('location_id')->nullable();
            $table->foreign('location_id')->references('id')->on('locations')->onDelete('cascade');

            $table->unsignedBigInteger('user_id')->nullable();
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');   
            $table->timestamps();
            
            

           });
    }
    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
