<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Carbon;
use App\Models\logs;
use App\Models\alerts;
use App\Models\locations;
use Illuminate\Support\Facades\Schema;


class LogsController extends Controller
{
    public function StoreAttendance()
    {


        $user = Auth::user();

        $todaytime = date("H:i:s");
        $todaydate = date("Y-m-d");
        $day = date('D', strtotime($todaydate));
        $schedtable  = str_replace(".", "_", $user->username);
        $evall = 0;
        $ipAddress = request()->ip(); // anywhere else


        if (logs::where('date', $todaydate)->where('user_id', Auth::id())->exists()) {
            // logs::where('date', $todaydate)->update(['action' => 'User logged in again']);
        } else {



                if (Schema::hasColumn('sched', $schedtable)) {
                    $user_shift = DB::table('sched')
                        ->where('date', $todaydate)
                        ->value($schedtable);




            if ($user_shift) {

                if (in_array($user_shift, ['Regular_AM', 'Regular_BW', 'Regular_PM', 'Shift_AM', 'Shift_PM'])) {

                    $location_data = locations::find($user->location_id);

                    // Set shift time based on shift type
                    switch ($user_shift) {
                        case 'Regular_AM':
                            $shiftTime = strtotime($day === 'Fri' ? $location_data->fullshift_shift : $location_data->am_shift);
                            break;

                        case 'Regular_BW':

                            $shiftTime = strtotime($day === 'Fri' ? $location_data->fullshift_shift : $location_data->bw_shift);

                            break;

                        case 'Regular_PM':

                            if ($day === 'Fri') {
                                $shiftTime = strtotime($location_data->fullshift_shift);
                            } elseif ($day === 'Thu') {
                                $shiftTime = strtotime($location_data->regular_shift);
                            } else {
                                $shiftTime = strtotime($location_data->pm_shift);
                            }

                            break;

                        case 'Shift_AM':

                            $shiftTime = strtotime($day === 'Fri' ? $location_data->fullshift_shift : $location_data->fullshift_am);

                            break;

                        case 'Shift_PM':


                            if ($day === 'Fri') {
                                $shiftTime = strtotime($location_data->fullshift_shift);
                            } elseif ($day === 'Thu') {
                                $shiftTime = strtotime($location_data->regular_shift);
                            } else {
                                $shiftTime = strtotime($location_data->fullshift_pm);
                            }


                            break;
                    }

                    // Attendance evaluation windows
                    $inam1 = date('H:i:s', $shiftTime - 15 * 60); // 15 minutes early
                    $inam2 = date('H:i:s', $shiftTime - 10 * 60); // 10 minutes early
                    $inam3 = date('H:i:s', $shiftTime - 5 * 60);  // 5 minutes early

                    // Default values
                    $evall = 0;
                    $comment = null;

                    // Evaluate based on attendance time
                    if ($todaytime <= $inam1) {
                        $evall = 1; // On time
                    } elseif ($todaytime <= $inam2) {
                        $evall = 0.5; // Slightly late
                    } elseif ($todaytime < $inam3) {
                        $evall = 0.25; // Very late
                    } else {
                        $evall = 0;
                        $comment = 'Late Attendance';
                    }
                }
            }

        } else {

            $user_shift = 'No Shift';
            $evall = 0;
            $comment = 'No Shift';

        }
                    
                    
            logs::create([
                'date' => $todaydate,
                'day' => $day,
                'user_id' => Auth::id(),
                'signin' => $todaytime,
                'nickname' => $user->nick_name,
                'location_id' =>  $user->location_id,
                'location' =>  $user->location_name,
                'eval' =>   $evall,
                'shift' =>   $user_shift,
                'ip' =>   $ipAddress,
                'comment' =>   $comment ?? null,
                'username' => $user->username


            ]);


            if ($comment === 'Late Attendance') {


                alerts::create([
                    'event_date' => $todaydate,
                    'type' => 'Attendance',
                    'user_id' => Auth::id(),
                    'comment' => 'Late Attendance',
                    'location_id' =>  $user->location_id

                ]);
            }
        }

        return redirect()->intended(route('dashboard', absolute: false));
    }


    public function AttendaceView()
    {

        // $packagesdata = logs::orderBy('created_at', 'desc')->paginate(30);

        if (in_array(Auth::user()->role_name, ['Agent', 'Leader', 'Senior', 'Supervisor'])) {

            $query = logs::where('location_id', Auth::user()->location_id);
        
            if (in_array(Auth::user()->role_name, ['Agent', 'Leader'])) {

                $query->where('user_id', Auth::user()->user_id);
            }
        
            $packagesdata = $query->orderBy('created_at', 'desc')->paginate(30);
        } else {
            $packagesdata = logs::orderBy('created_at', 'desc')->paginate(30);
        }


        return view('logs', ['packages' => $packagesdata, 'searchcat' => '']);
    }



    public function AlertsView()
    {
        $user = Auth::user();

        // $packagesdata = alerts::orderBy('created_at', 'desc')->paginate(30);

        if (in_array(Auth::user()->role_name, ['Agent', 'Leader', 'Senior', 'Supervisor'])) {

            $query = alerts::where('location_id', Auth::user()->location_id);
        
            if (in_array(Auth::user()->role_name, ['Agent', 'Leader'])) {

                $query->where('user_id', Auth::user()->user_id);
            }
        
            $packagesdata = $query->orderBy('created_at', 'desc')->paginate(30);
        } else {
            $packagesdata = alerts::orderBy('created_at', 'desc')->paginate(30);
        }

         alerts::where('user_id', $user->id)->update(['status' => 'Viewed']);


        return view('alerts', ['alerts' => $packagesdata, 'searchcat' => '']);
    }

}
