<?php
use Illuminate\Support\Facades\DB;

use App\Http\Controllers\system_langsControler;
use App\Models\system_langs;

$lang = system_langs::all();
$arr = [];
b:
foreach ($lang as $product) {
    $arr['trans'][$product->lang_key] = $product->english;
}
$filstatus = '';
$street = '';

?>


@extends('layouts.master_admin')







@section('admincontent')
    <!--begin::Content-->
    <div id="kt_app_content" class="app-content px-lg-3">
        <!--begin::Content container-->
        <div id="kt_app_content_container" class="app-container container-fluid">
            <!--begin::Table-->
            <div class="app-toolbar-wrapper d-flex flex-stack flex-wrap gap-4 w-100">
                <!--begin::Page title-->
                <div class="page-title d-flex align-items-center gap-1 me-3">
                    <!--begin::Title-->
                    <h1
                        class="page-heading d-flex flex-column justify-content-center text-gray-900 lh-1 fw-bolder fs-2x my-0 me-5">
                        @lang('translate.Devices')</h1>
                    <!--end::Title-->
                    <!--begin::Breadcrumb-->
                    <ul class="breadcrumb breadcrumb-separatorless fw-semibold">
                        &nbsp;&nbsp;&nbsp;
                        <li class="breadcrumb-item text-gray-700 fw-bold lh-1">
                            <a href="index.php" class="text-gray-500 text-hover-primary">
                                <i class="ki-duotone ki-home fs-3 text-gray-500 mx-n1"></i>
                            </a>
                        </li>
                        <!--end::Item-->
                        <!--begin::Item-->

                    </ul>
                    <!--end::Breadcrumb-->
                </div>
                <!--end::Page title-->
                <!--begin::Actions-->
                <div class="d-flex align-items-center gap-2 gap-lg-3 flex-shrink-0">

                    <a onclick=" $('#kt_modal_new_target').modal('show');"
                        class="btn btn-sm btn-success d-flex flex-center ms-3 px-4 py-3">
                        <i class="ki-duotone ki-plus-square fs-2">
                            <span class="path1"></span>
                            <span class="path2"></span>
                            <span class="path3"></span>
                        </i>
                        &nbsp;&nbsp;&nbsp;

                        <span> @lang('translate.Add New')</span>
                    </a>
                </div>
                <!--end::Actions-->
            </div>
            <!--end::Toolbar wrapper-->
            <br>
            <div class="card">
                <div class="card-header border-0 pt-6">
                    <!--begin::Card title-->
                    <div class="card-title">
                        <form action="{{ route('searchmobile') }}" id="form1" method="post">
                            @csrf()


                            <div class="d-flex align-items-center position-relative my-1">
                                <i class="ki-duotone ki-magnifier fs-3 position-absolute ms-5">
                                    <span class="path1"></span>
                                    <span class="path2"></span>
                                </i>
                                {{-- {{$categorydata -> category}} --}}

                                <input type="text" class="form-control form-control-solid  ps-13"
                                    style="width: 100%;text-align: center;" placeholder=" @lang('translate.Search')"
                                    name="searchcategory" value="{{ $searchcat }}" />&nbsp;&nbsp;&nbsp;
                                <button type="submit" class="btn btn-primary">
                                    <i class="fa fa-search fs-2"></i></button>
                            </div>
                            <!--end::Search-->

                        </form>

                    </div>
                    <!--begin::Card title-->
                    <!--begin::Card toolbar-->
                    <div class="card-toolbar">
                        <!--begin::Toolbar-->
                        <div class="d-flex justify-content-end" data-kt-user-table-toolbar="base" id="bunsec">
                            <!--begin::Filter-->
                            <button type="button" class="btn btn-light-primary me-3" data-kt-menu-trigger="click"
                                data-kt-menu-placement="bottom-end">
                                <i class="ki-duotone ki-filter fs-2">
                                    <span class="path1"></span>
                                    <span class="path2"></span>
                                </i>&nbsp;&nbsp; @lang('translate.Filter')</button>
                            <!--begin::Menu 1-->
                            <div class="menu menu-sub menu-sub-dropdown w-300px w-md-325px" data-kt-menu="true">
                                <!--begin::Header-->
                                <div class="px-7 py-5">
                                    <div class="fs-5 text-gray-900 fw-bold"> @lang('translate.Filter Options')</div>
                                </div>
                                <!--end::Header-->
                                <!--begin::Separator-->
                                <div class="separator border-gray-200"></div>
                                <!--end::Separator-->
                                <!--begin::Content-->

                                <form action="lawers/index.php" id="form2" method="get">


                                    <div class="px-7 py-5" data-kt-user-table-filter="form" dir="ltr">
                                        <!--begin::Input group-->

                                        <div class="mb-10">
                                            <label class="form-label fs-6 fw-semibold">@lang('translate.Date From')
                                                :</label>

                                            <input type="date" class="form-control form-control-solid" name="fildatefrom"
                                                value="" />

                                        </div>


                                        <div class="mb-10">
                                            <label class="form-label fs-6 fw-semibold">@lang('translate.Date To')
                                                :</label>

                                            <input type="date" class="form-control form-control-solid" name="fildateto"
                                                value="" />

                                        </div>


                                        <!--begin::Input group-->
                                        <div class="mb-10">
                                            <label class="form-label fs-6 fw-semibold">@lang('translate.Status')
                                                :</label>
                                            <select class="form-select form-select-solid fw-bold" data-kt-select2="true"
                                                data-placeholder="Select option" data-allow-clear="true" name="filstatus"
                                                data-kt-user-table-filter="two-step" data-hide-search="true">
                                                <option value="<?php echo $filstatus; ?>"><?php if (empty($filstatus)) {
                                                    $filstatust = $arr['trans']['Status'];
                                                    echo $filstatust;
                                                } else {
                                                    echo $arr['trans'][$filstatus];
                                                } ?></option>
                                                <option value="Active">{{ $arr['trans']['Active'] }}</option>
                                                <option value="Suspend">{{ $arr['trans']['Suspend'] }}</option>
                                            </select>
                                        </div>
                                        <!--end::Input group-->


                                        <!--begin::Actions-->
                                        <div class="d-flex justify-content-end">
                                            <button type="submit"
                                                class="btn btn-light btn-active-light-primary fw-semibold me-2 px-6"
                                                data-kt-menu-dismiss="true" name="reset" value="reset"
                                                data-kt-user-table-filter="reset">@lang('translate.Reset')</button>
                                            <button type="submit" class="btn btn-primary fw-semibold px-6"
                                                data-kt-menu-dismiss="true"
                                                data-kt-user-table-filter="filter">@lang('translate.Apply')</button>
                                        </div>
                                        <!--end::Actions-->
                                    </div>
                                    <!--end::Content-->

                                </form>
                            </div>

                            <button type="button" class="btn btn-light-primary me-3" id="modexport">
                                <i class="ki-duotone ki-exit-up fs-2">
                                    <span class="path1"></span>
                                    <span class="path2"></span>
                                </i>&nbsp;&nbsp; @lang('translate.Export')</button>

                            <button type="button" class="btn btn-primary" style="display: none;" id="appiontbutt"
                                data-bs-toggle="modal" data-bs-target="#kt_modal_add_user">
                                <i class="ki-duotone ki-plus fs-2"></i>Add New</button>&nbsp;&nbsp;&nbsp;

                            <button type="button" class="btn btn-danger" data-kt-user-table-select="delete_selected"
                                style="display: none; padding: 5px 5px;text-align: center;" id="delsel"
                                onclick="deleteall();"> <i class="fa fa-trash-o"
                                    style="font-size:25px"></i>&nbsp;</button>



                        </div>

                        <div class="modal" style="display: none;" id="mymodcustwo" dir="ltr">
                            <!--begin::Modal dialog-->
                            <div class="modal-dialog modal-dialog-centered mw-650px">
                                <!--begin::Modal content-->
                                <div class="modal-content">
                                    <!--begin::Modal header-->
                                    <div class="modal-header">
                                        <!--begin::Modal title-->
                                        <h2 class="fw-bold">@lang('translate.Export')</h2>
                                        <!--end::Modal title-->
                                        <!--begin::Close-->
                                        <div class="btn btn-icon btn-sm btn-active-icon-primary closeex"
                                            data-kt-users-modal-action="close">
                                            <i class="ki-duotone ki-cross fs-1">
                                                <span class="path1"></span>
                                                <span class="path2"></span>
                                            </i>
                                        </div>
                                        <!--end::Close-->
                                    </div>
                                    <!--end::Modal header-->
                                    <!--begin::Modal body-->
                                    <div class="modal-body scroll-y mx-5 mx-xl-15 my-7">
                                        <!--begin::Form-->
                                        <form action="{{ route('exportdevices') }}" id="form3" method="post" role="form3" enctype='multipart/form-data'>
                                            @csrf
                                            <!--begin::Input group-->
                                            <div class="fv-row mb-10">




                                                <!--begin::Label-->
                                                <label class="required fs-6 fw-semibold form-label mb-2">Select Export
                                                    Format:</label>
                                                <!--end::Label-->
                                                <!--begin::Input-->
                                                <select name="format" required data-placeholder="Select a format"
                                                    data-hide-search="true" class="form-select form-select-solid fw-bold">
                                                    <option value="excel">Excel</option>
                                                </select>
                                                <!--end::Input-->
                                            </div>
                                            <!--end::Input group-->
                                            <!--begin::Actions-->
                                            <div class="text-center">
                                                <button type="reset" id="excancel" class="btn btn-light me-3"
                                                    data-kt-users-modal-action="cancel">@lang('translate.Cancel')</button>
                                                <button type="submit" name="export" class="btn btn-primary">
                                                    <span class="indicator-label">@lang('translate.Save')</span>
                                                    <span class="indicator-progress">Please wait...
                                                        <span
                                                            class="spinner-border spinner-border-sm align-middle ms-2"></span></span>
                                                </button>
                                            </div>
                                            <!--end::Actions-->
                                        </form>
                                        <!--end::Form-->
                                    </div>
                                    <!--end::Modal body-->
                                </div>
                                <!--end::Modal content-->
                            </div>
                            <!--end::Modal dialog-->
                        </div>
                        <!--end::Modal - New Card-->
                        <!--begin::Modal - Add task-->






                        <!--begin::Modal - New Target-->
                        <div class="modal fade" id="kt_modal_new_target" tabindex="-1" aria-hidden="true">
                            <!--begin::Modal dialog-->
                            <div class="modal-dialog modal-dialog-centered mw-650px">
                                <!--begin::Modal content-->
                                <div class="modal-content rounded">
                                    <!--begin::Modal header-->
                                    <div class="modal-header pb-0 border-0 justify-content-end">
                                        <!--begin::Close-->
                                        <div class="btn btn-sm btn-icon btn-active-color-primary" data-bs-dismiss="modal">
                                            <i class="ki-duotone ki-cross fs-1">
                                                <span class="path1"></span>
                                                <span class="path2"></span>
                                            </i>
                                        </div>
                                        <!--end::Close-->
                                    </div>

                                    <!--begin::Modal header-->
                                    <!--begin::Modal body-->
                                    <div class="modal-body scroll-y px-10 px-lg-15 pt-0 pb-15">
                                        <!--begin:Form-->
                                        <form action="{{ route('storelandline') }}" id="kt_modal_new_target_form"
                                            method="post" enctype='multipart/form-data'>

                                            @csrf()
                                            <!--begin::Heading-->
                                            <div class="mb-13 text-center">
                                                <!--begin::Title-->
                                                <h1 class="mb-3">Add ADSL</h1>
                                                <!--end::Title-->

                                            </div>
                                            <!--end::Heading-->
                                            <!--begin::Input group-->
                                            <div class="d-flex flex-column mb-8">
                                                <label class="fs-6 fw-semibold mb-2">@lang('translate.Number')</label>
                                                <input type="text" class="form-control form-control-solid"
                                                    id="new_num" name="new_num" placeholder="Number">
                                            </div>
                                            <!--end::Input group-->
                                            <!--begin::Input group-->
                                            <div class="d-flex flex-column mb-8">
                                                <label class="fs-6 fw-semibold mb-2">@lang('translate.Type')</label>
                                                <select name="new_type" class="form-select form-select-lg fw-semibold">

                                                    <option value=""> Select Type</option>
                                                    <option value="New ADSL">New ADSL</option>
                                                    <option value="New Domain">New Domain</option>
                                                </select>
                                            </div>
                                            <!--end::Input group-->


                                            <!--begin::Input group-->
                                            <div class="d-flex flex-column mb-8">
                                                <label class="fs-6 fw-semibold mb-2">@lang('translate.Package')</label>

                                                <select name="new_package" id="new_package"
                                                    aria-label="@lang('translate.package')"
                                                    class="form-select form-select-lg fw-semibold">
                                                    <option value=""> Select Package</option>
                                                    @foreach ($packages as $package)
                                                        {{-- <option value="{{ $catigory->id }}"> {{ $catigory->category }} </option> --}}
                                                        <option value="{{ $package->id }}"> {{ $package->package_name }}
                                                        </option>
                                                    @endforeach

                                                </select>


                                            </div>
                                            <!--end::Input group-->


                                            <!--begin::Input group-->
                                            <div class="d-flex flex-column mb-8">
                                                <label class="fs-6 fw-semibold mb-2">Comment</label>
                                                <textarea class="form-control form-control-solid" rows="4" name="nwcomment" id="nwcomment"
                                                    placeholder="Comment"></textarea>
                                            </div>
                                            <!--end::Input group-->


                                            <!--begin::Actions-->
                                            <div class="text-center">
                                                <button type="button" data-bs-dismiss="modal"
                                                    class="btn btn-light me-3">Cancel</button>

                                                <button type="submit" name="submit" class="btn btn-primary">
                                                    <span class="indicator-label">Submit</span>
                                                    <span class="indicator-progress">Please wait...
                                                        <span
                                                            class="spinner-border spinner-border-sm align-middle ms-2"></span></span>
                                                </button>
                                            </div>
                                            <!--end::Actions-->
                                        </form>
                                        <!--end:Form-->
                                    </div>
                                    <!--end::Modal body-->
                                </div>
                                <!--end::Modal content-->
                            </div>
                            <!--end::Modal dialog-->
                        </div>
                        <!--end::Modal - New Target-->







                        <!--begin::Modal - New Target-->
                        <div class="modal fade" id="kt_modal_add_user" tabindex="-1" aria-hidden="true">
                            <!--begin::Modal dialog-->
                            <div class="modal-dialog modal-dialog-centered mw-650px">
                                <!--begin::Modal content-->
                                <div class="modal-content rounded">
                                    <!--begin::Modal header-->
                                    <div class="modal-header pb-0 border-0 justify-content-end">
                                        <!--begin::Close-->
                                        <div class="btn btn-sm btn-icon btn-active-color-primary" data-bs-dismiss="modal">
                                            <i class="ki-duotone ki-cross fs-1">
                                                <span class="path1"></span>
                                                <span class="path2"></span>
                                            </i>
                                        </div>
                                        <!--end::Close-->
                                    </div>

                                    <!--begin::Modal header-->
                                    <!--begin::Modal body-->
                                    <div class="modal-body scroll-y px-10 px-lg-15 pt-0 pb-15">
                                        <!--begin:Form-->
                                        <form action="{{ route('storelandline') }}" id="kt_modal_add_user_form"
                                            method="post" enctype='multipart/form-data'>

                                            @csrf()
                                            <!--begin::Heading-->
                                            <div class="mb-13 text-center">
                                                <!--begin::Title-->
                                                <h1 class="mb-3">Update ADSL</h1>
                                                <!--end::Title-->

                                            </div>
                                            <!--end::Heading-->


                                            <input type="text" name="cusappid" id="cusappid" style="display: none;"
                                                class="form-control form-control-solid mb-3 mb-lg-0" />

                                            <!--begin::Input group-->
                                            <div class="d-flex flex-column mb-8">
                                                <label class="fs-6 fw-semibold mb-2">@lang('translate.Number')</label>
                                                <input type="text" class="form-control form-control-solid"
                                                    id="up_num" name="up_num" placeholder="Number">
                                            </div>
                                            <!--end::Input group-->
                                            <!--begin::Input group-->
                                            <div class="d-flex flex-column mb-8">
                                                <label class="fs-6 fw-semibold mb-2">@lang('translate.Type')</label>
                                                <select name="up_type" id="up_type"
                                                    class="form-select form-select-lg fw-semibold">

                                                    <option value=""> Select Type</option>
                                                    <option value="New ADSL">New ADSL</option>
                                                    <option value="New Domain">New Domain</option>
                                                </select>
                                            </div>
                                            <!--end::Input group-->


                                            <!--begin::Input group-->

                                            <!--begin::Input group-->
                                            <div class="d-flex flex-column mb-8">
                                                <label class="fs-6 fw-semibold mb-2">@lang('translate.Package')</label>

                                                <select name="up_package" id="up_package" aria-label="@lang('translate.package')"
                                                    class="form-select form-select-lg fw-semibold">
                                                    <option value=""> Select Package</option>
                                                    @foreach ($packages as $package)
                                                        {{-- <option value="{{ $catigory->id }}"> {{ $catigory->category }} </option> --}}
                                                        <option value="{{ $package->id }}"> {{ $package->package_name }}
                                                        </option>
                                                    @endforeach

                                                </select>


                                            </div>
                                            <!--end::Input group-->


                                            <!--begin::Input group-->
                                            <div class="d-flex flex-column mb-8">
                                                <label class="fs-6 fw-semibold mb-2">Comment</label>
                                                <textarea class="form-control form-control-solid" rows="4" name="up_comment" id="up_comment"
                                                    placeholder="Comment"></textarea>
                                            </div>
                                            <!--end::Input group-->




                                            <!--begin::Actions-->
                                            <div class="text-center">
                                                <button type="button" data-bs-dismiss="modal"
                                                    class="btn btn-light me-3">Cancel</button>

                                                <button type="submit" name="submit" class="btn btn-primary">
                                                    <span class="indicator-label">Submit</span>
                                                    <span class="indicator-progress">Please wait...
                                                        <span
                                                            class="spinner-border spinner-border-sm align-middle ms-2"></span></span>
                                                </button>
                                            </div>
                                            <!--end::Actions-->
                                        </form>
                                        <!--end:Form-->
                                    </div>
                                    <!--end::Modal body-->
                                </div>
                                <!--end::Modal content-->
                            </div>
                            <!--end::Modal dialog-->
                        </div>
                        <!--end::Modal - New Target-->









                    </div>
                    <!--end::Card toolbar-->
                </div>
                <!--end::Card header-->




                <!--begin::Card body-->
                <div class="card-body py-4">
                    <div class="table-responsive">


                        <table class="table align-middle table-row-bordered table-row-solid gy-4 gs-9 table-bordered" dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}">
                            <!--begin::Thead-->
                            <thead class="table-light">
                                <tr class="text-start text-muted fw-bold fs-7 text-uppercase gs-0">
                                    <th class="w-10px pe-2">
                                        <div class="form-check form-check-sm form-check-custom form-check-solid me-3">
                                            <input class="form-check-input" type="checkbox" name="select-all"
                                                id="all" value="all" onclick="test(this);" />


                                        </div>
                                    </th>



                                    <th class="min-w-50px dirside" onclick="numberTableSort(this,true,'en')">
                                        @lang('translate.Date')</th>
                                    <th class="min-w-100px dirside" onclick="numberTableSort(this,true,'en')">
                                        @lang('translate.SKU')</th>
                                    <th class="min-w-100px dirside" onclick="numberTableSort(this,true,'en')">
                                        @lang('translate.Number')</th>

                                    <th class="min-w-50px dirside" onclick="numberTableSort(this,true,'en')">
                                        @lang('translate.Name')</th>
                                    <th class="min-w-50px dirside" onclick="numberTableSort(this,true,'en')">
                                        @lang('translate.Price')</th>
                                    <th class="min-w-50px dirside" onclick="numberTableSort(this,true,'en')">
                                        @lang('translate.Type')</th>
                                    <th class="min-w-50px dirside" onclick="numberTableSort(this,true,'en')">
                                        @lang('translate.Username')</th>
                                    <th class="min-w-50px dirside" onclick="numberTableSort(this,true,'en')">
                                        @lang('translate.Location')</th>

                                    @if (in_array(Auth::user()->role_name, ['Senior', 'Supervisor', 'Admins']))
                                        <th class="text-end min-w-100px dirside1">@lang('translate.Actions')</th>
                                    @endif


                                </tr>
                            </thead>

                            <tbody class="text-gray-600 fw-semibold">
                                <!-- Data will be loaded here -->


                                @foreach ($devices as $device)
                                    <tr>
                                        <td>
                                            <div class="form-check form-check-sm form-check-custom form-check-solid">
                                                <input class="form-check-input" type="checkbox" id="{{ $device->id }}"
                                                    name="select" value="{{ $device->id }}" onclick="test(this);" />
                                            </div>
                                        </td>


                                        <td>{{ $device->cashbox_date }}</td>
                                        <td id="con{{ $device->id }}">{{ $device->product_sku }}</td>

                                        <td id="pon{{ $device->id }}">{{ $device->number }}</td>
                                        <td id="pon{{ $device->id }}">{{ $device->product_name }}</td>
                                        <td id="pon{{ $device->id }}">{{ $device->price }}</td>

                                        <td id="pak1{{ $device->id }}">{{ $device->packages->package_name }}</td>

                                        <td style="display: none;" id="pak{{ $device->id }}">
                                            {{ $device->packages->id }}</td>

                                        <td>{{ $device->users->nick_name }}</td>
                                        <td>{{ $device->locationval->name_en }}</td>

                                        @if (in_array(Auth::user()->role_name, ['Senior', 'Supervisor', 'Admins']))
                                            <td class="text-end" cope="row" data-label="@lang('translate.Actions')">
                                                <a href="#"
                                                    class="btn btn-light btn-active-light-primary btn-flex btn-center btn-sm"
                                                    data-kt-menu-trigger="click"
                                                    data-kt-menu-placement="bottom-end">@lang('translate.Actions')
                                                    <i class="ki-duotone ki-down fs-5 ms-1"></i></a>
                                                <!--begin::Menu-->
                                                <div class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-600 menu-state-bg-light-primary fw-semibold fs-7 w-125px py-4"
                                                    data-kt-menu="true">


                                                    <!--begin::Menu item-->
                                                    <div class="menu-item px-3">

                                                        <a id="{{ $device->id }}" onclick="appoint(this);"
                                                            class="menu-link px-3"> @lang('translate.Edit')</a>


                                                    </div>
                                                    <!--end::Menu item-->



                                                    <!--begin::Menu item-->
                                                    <div class="menu-item px-3">
                                                        <a href="/deletecategory/{{ $device->id }}"
                                                            style="color: red;cursor: pointer;" class="menu-link px-3"
                                                            data-kt-users-table-filter="delete_row">@lang('translate.Delete')</a>
                                                    </div>
                                                    <!--end::Menu item-->
                                                </div>
                                                <!--end::Menu-->
                                            </td>
                                        @endif

                                    </tr>
                                @endforeach

                            </tbody>
                        </table>
                        <!--end::Table-->


                    </div>


                    {{ $devices->links() }}
                    <style>
                        svg {

                            height: 20px !important;
                        }

                        .hidden {
                            padding: 10px !important;
                        }
                    </style>


                </div>
                <!--end::Card body-->
            </div>
            <!--end::Card-->
        </div>
        <!--end::Content container-->
    </div>
    <!--end::Content-->
    </div>

    </div>
    <!--end:::Main-->
    </div>
    <!--end::Wrapper-->
    </div>
    <!--end::Page-->
    </div>
    <!--end::App-->
@endsection


@section('adminscripts')
    <script>
        function appoint(event) {


            var valuidd = event.id;



            phonenum = document.getElementById('pon' + valuidd).innerText;
            contype = document.getElementById('con' + valuidd).innerText;
            packagenm = document.getElementById('pak' + valuidd).innerText;
            commentty = document.getElementById('com' + valuidd).innerText;


            document.getElementById("up_num").value = phonenum;
            document.getElementById("up_type").value = contype;
            document.getElementById("up_package").value = packagenm.trim();;
            document.getElementById("up_comment").value = commentty;
            document.getElementById("cusappid").value = valuidd;




            document.getElementById("appiontbutt").click();


        }
    </script>
    <script>
        document.getElementById('modexport').addEventListener('click', function() {
            document.getElementById('mymodcustwo').style.display = 'block';
        });

        document.querySelector('.closeex').addEventListener('click', function() {
            document.getElementById('mymodcustwo').style.display = 'none';
        });

        document.getElementById('excancel').addEventListener('click', function() {
            document.getElementById('mymodcustwo').style.display = 'none';
        });
    </script>
@endsection
