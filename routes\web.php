<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\MobileControler;
use App\Http\Controllers\ADSLController;
use App\Http\Controllers\DevicesController;
use App\Http\Controllers\LandlineController;
use App\Http\Controllers\AppDetailsConroller;
use App\Http\Controllers\PackagesControler;
use App\Http\Controllers\ProductsControler;
use App\Http\Controllers\DashboardController;
use App\Http\Controllers\MembersController;
use App\Http\Controllers\ExcelController;
use App\Http\Controllers\CashierController;
use App\Http\Controllers\ScheduleController;
use App\Http\Controllers\TargetController;
use App\Http\Controllers\ReportsController;
use App\Http\Controllers\WarehouseController;
use App\Http\Controllers\system_langControler;
use App\Http\Controllers\AccountController;
use App\Http\Controllers\ChatController;
use App\Http\Controllers\LogsController;
use Illuminate\Support\Facades\Route;
use App\Http\Middleware\LanguageMiddleware;


Route::get('/', function () {
    return view('welcome');
});

Route::get('/attendance/store', [LogsController::class, 'StoreAttendance'])->name('storeattendance')->middleware('auth');
Route::get('/dashboard', [DashboardController::class, 'DashboardRedirect'])->name('dashboard')->middleware('auth');
Route::get('/dashboarduser', [DashboardController::class, 'DashboardUserView'])->name('dashboarduser')->middleware('auth');


Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::patch('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::delete('/profile', [ProfileController::class, 'destroy'])->name('profile.destroy');
    
});

require __DIR__.'/auth.php';

Route::post('/profile/update-password', [AccountController::class, 'updatePassword'])->name('profile.updatePassword');


Route::get('/chartdata1', [DashboardController::class, 'ChartData1']);
Route::get('/chartdata2', [DashboardController::class, 'ChartData2']);

Route::get('/chartdata3', [MembersController::class, 'ChartData3']);
Route::get('/chartdata4', [MembersController::class, 'ChartData4']);

Route::get('/translation', [system_langControler::class, 'SysLang'])->name('syslang')->middleware('auth');
Route::get('/locations', [AppDetailsConroller::class, 'LocationsView'])->name('locationsview')->middleware('auth');
Route::post('/addnewlangauge', [system_langControler::class, 'AddNewLang'])->name('addnewlang')->middleware('auth');

Route::get('/mobile', [MobileControler::class, 'NewMobile'])->name('newmobile')->middleware('auth');
Route::get('/mobileview', [MobileControler::class, 'MobileView'])->name('mobileview')->middleware('auth');
Route::post('/storemobile',  [MobileControler::class, 'StoreMobile'])->name('storemobile')->middleware('auth');
Route::get('/deletemobile/{catid}', [MobileControler::class, 'DeleteMobile'])->name('deletemobile')->middleware('auth');
Route::post('/mobilesearch', [MobileControler::class, 'SearchMobile'])->name('searchmobile')->middleware('auth');
Route::post('/mobilefilter', [MobileControler::class, 'FilterMobile'])->name('filtermobile')->middleware('auth');

Route::get('/adslview', [ADSLController::class, 'ADSLView'])->name('adslview')->middleware('auth');
Route::post('/adslsearch', [ADSLController::class, 'SearchADSL'])->name('searchadsl')->middleware('auth');
Route::post('/adslfilter', [ADSLController::class, 'FilterADSL'])->name('filteradsl')->middleware('auth');

Route::get('/devicesview', [DevicesController::class, 'DevicesView'])->name('devicesview')->middleware('auth');
Route::post('/devicessearch', [DevicesController::class, 'SearchDevices'])->name('searchdevices')->middleware('auth');
Route::post('/devicesfilter', [DevicesController::class, 'FilterDevices'])->name('filterdevices')->middleware('auth');

Route::get('/landlineview', [LandlineController::class, 'LandlineView'])->name('landlineview')->middleware('auth');
Route::post('/landlinesearch', [LandlineController::class, 'SearchLandline'])->name('searchlandline')->middleware('auth');
Route::post('/landlinefilter', [LandlineController::class, 'FilterLandline'])->name('filterlandline')->middleware('auth');

Route::get('/excel_uploads/stock_upload', [ExcelController::class, 'viewStockUpload'])->middleware('auth');
Route::get('/excel_uploads/uploadmobile', [ExcelController::class, 'viewMobileUpload'])->middleware('auth');
Route::get('/excel_uploads/uploadadsl', [ExcelController::class, 'viewAdslUpload'])->middleware('auth');
Route::get('/excel_uploads/uploadcashbox', [ExcelController::class, 'UploadCashbox'])->middleware('auth');

Route::post('/stock/import', [ExcelController::class, 'StockUpload'])->name('stockupload')->middleware('auth');
Route::post('/stock/delete', [ExcelController::class, 'DeleteAll'])->name('deleteall')->middleware('auth');
Route::post('/mobile/delete', [ExcelController::class, 'DeleteAllMobile'])->name('deleteallmobile')->middleware('auth');

Route::get('/exportschedule', [ExcelController::class, 'ExportSchedule'])->name('exportschedule')->middleware('auth');
Route::post('/importschedule', [ExcelController::class, 'ImportSchedule'])->name('importschedule')->middleware('auth');

Route::get('/cashier/addcashier', [CashierController::class, 'AddCashier'])->name('addcashier')->middleware('auth');
Route::get('/cashier/editcashier/{memid}', [CashierController::class, 'EditCashier'])->name('editcashier')->middleware('auth');
Route::get('/deletecashier/{catid}', [CashierController::class, 'DeleteCashier'])->middleware('auth');
Route::post('/searchcashier', [CashierController::class, 'SearchCashier'])->name('searchcashier');
Route::post('/filtercashier', [CashierController::class, 'FilterCashier'])->name('filtercashier');




Route::post('/importcashbox', [ExcelController::class, 'CashBoxData'])->name('cashboxdata');
Route::post('/importwallet', [ExcelController::class, 'Walletdata'])->name('walletdata');
Route::post('/importmobile', [ExcelController::class, 'Mobiledata'])->name('mobiledata');
Route::post('/importadsl', [ExcelController::class, 'ADSLdata'])->name('adsledata');
Route::post('/storecashier',  [CashierController::class, 'StoreCashier'])->name('storecashier')->middleware('auth');



Route::get('/cashier/cashierIiesview', [CashierController::class, 'CashierReportView'])->name('cashierreportview')->middleware('auth');


Route::get('/cashier/cashierview', [CashierController::class, 'CashierView'])->name('cashierview')->middleware('auth');
Route::get('/cashier/cashier_report', [CashierController::class, 'CashierReport'])->name('cashierreport')->middleware('auth');
Route::post('/cashier/cashier_report_filter', [CashierController::class, 'CashierReportFilter'])->name('cashierreportfilter')->middleware('auth');


Route::get('/cashier/fraction_new', [CashierController::class, 'NewFraction'])->name('newfraction')->middleware('auth');
Route::post('/storefraction',  [CashierController::class, 'StoreFraction'])->name('storefraction')->middleware('auth');

Route::post('/getcashierfraction',  [CashierController::class, 'GetCashierFraction'])->name('getcashierfraction')->middleware('auth');
Route::get('/cashier/fractionview', [CashierController::class, 'FractionView'])->name('fractionview')->middleware('auth');

Route::get('/schedule', [ScheduleController::class, 'index'])->name('schedule.scheduleview')->middleware('auth');
Route::get('/schedule/fulltable', [ScheduleController::class, 'FullTable'])->name('schedule.fulltable')->middleware('auth');
Route::post('/schedule', [ScheduleController::class, 'filter'])->name('schedule.filter')->middleware('auth');
Route::get('/userannual', [ScheduleController::class, 'AnnualReport'])->name('annualreport')->middleware('auth');
Route::post('/annualview', [ScheduleController::class, 'AnnualView'])->name('annualview')->middleware('auth');
Route::get('/annualdetails', [ScheduleController::class, 'AnnualDetails'])->name('annualdetails')->middleware('auth');
Route::post('/annualdetailsview', [ScheduleController::class, 'AnnualDetailsView'])->name('annualdetailsview')->middleware('auth');


Route::middleware(['auth'])->group(function () {
    Route::get('/target-report', [TargetController::class, 'index'])->name('target.index')->middleware('auth');
    Route::post('/target-report', [TargetController::class, 'store'])->name('target.store')->middleware('auth');
})->middleware('auth');



Route::get('/report/agent-targets', [TargetController::class, 'showReport'])->name('showreport')->middleware('auth');
Route::get('/report/location-targets', [TargetController::class, 'ShowLocationReport'])->name('showlocationReport')->middleware('auth');
Route::get('/target/agentrank', [TargetController::class, 'AgentRank'])->name('agentrank')->middleware('auth');


Route::get('/report/users-targets', [ReportsController::class, 'ShowUsersTarget'])->name('showuserstarget')->middleware('auth');
Route::get('/report/usersper-targets', [ReportsController::class, 'ShowUsersPerTarget'])->name('showuserspertarget')->middleware('auth');

Route::get('/report/locationreport', [ReportsController::class, 'MonthlyReport'])->name('monthlyreport')->middleware('auth');

Route::get('/warehouse/view', [WarehouseController::class, 'WareHouseView'])->name('warehouseview')->middleware('auth');
Route::get('/warehouse/transaction', [WarehouseController::class, 'StockTransactionView'])->name('stocktransactionview')->middleware('auth');



Route::post('/storeannual',  [ScheduleController::class, 'StoreAnnualPlan'])->name('storeannualplan')->middleware('auth');


Route::get('/chart1', [DashboardController::class, 'getChartData'])->name('getchartData')->middleware('auth');


Route::get('/export-cashier', [ExcelController::class, 'exportCashierReport'])->name('exportcashierreport');


Route::get('/print-cashier', [CashierController::class, 'PrintCashier'])->name('printcashier');


Route::get('/members_view', [MembersController::class, 'MembersView'])->name('membersview')->middleware('auth');
Route::get('/member_view/{catid}', [MembersController::class, 'MemberView'])->name('memberview')->middleware('auth');

Route::post('/filtermembers', [MembersController::class, 'FilterMembers'])->name('filtermembers')->middleware('auth');

Route::get('/libraryview', [MembersController::class, 'LibraryView'])->name('libraryview')->middleware('auth');

Route::post('/storelibrary', [MembersController::class, 'StoreLibrary'])->name('storelibrary')->middleware('auth');

Route::get('/challengesview', [AppDetailsConroller::class, 'ChallengesView'])->name('challengesview')->middleware('auth');
Route::post('/challengesstore', [AppDetailsConroller::class, 'ChallengeStore'])->name('challengesstore')->middleware('auth');

Route::get('/changelang/{lang?}', [AccountController::class, 'ChangeLang'])->name('ChangeLanguge')->middleware(LanguageMiddleware::class);


Route::get('/members_add', [MembersController::class, 'MembersAdd'])->name('membersadd')->middleware('auth');
Route::get('/members_edit/{memid}', [MembersController::class, 'MembersEdit'])->name('membersedit')->middleware('auth');

Route::post('/storemember',  [MembersController::class, 'storeMember'])->name('storemember')->middleware('auth');
Route::post('/storpssword',  [MembersController::class, 'storePassword'])->name('storpssword')->middleware('auth');


Route::get('/account/profile', [MembersController::class, 'AccountProfile'])->name('accountprofile')->middleware('auth');
Route::post('/account/editprofile/{memid}', [AccountController::class, 'AccountEditProfile'])->name('accounteditprofile')->middleware('auth');
Route::get('/account/roles', [MembersController::class, 'AdminRoles'])->name('adminroles')->middleware('auth');

Route::get('/account/roles_view/{memid}', [MembersController::class, 'AdminROleview'])->name('adminroleview');

Route::get('/account/roles_add', [MembersController::class, 'AdminROleAdd'])->name('adminroleadd');

Route::post('/addroles', [MembersController::class, 'AdminROleStore'])->name('adminrolestore');
Route::post('/updateroles', [MembersController::class, 'AdminROleEdit'])->name('adminroleedit');



Route::post('/storelandline',  [LandlineController::class, 'StoreLandline'])->name('storelandline')->middleware('auth');
Route::post('/storelocation',  [AppDetailsConroller::class, 'StoreLocation'])->name('storelocation')->middleware('auth');

Route::get('/tools/setting', [AppDetailsConroller::class, 'SettingView'])->name('settingview')->middleware('auth');

Route::post('/storegeneral',  [AppDetailsConroller::class, 'StoteAdimGeneral'])->name('storeadmingneral')->middleware('auth');
Route::post('/storestore',  [AppDetailsConroller::class, 'StoteAdimStore'])->name('stoteAdimStore')->middleware('auth');
Route::post('/storelocationad',  [AppDetailsConroller::class, 'StoteAdimLocation'])->name('stoteAdimLocation')->middleware('auth');
Route::post('/storelogo',  [AppDetailsConroller::class, 'StoteAdimLogo'])->name('stoteAdimLogo')->middleware('auth');
Route::get('/chat',  [ChatController::class, 'ChatView'])->name('chatview')->middleware('auth');
Route::get('/chat/messages', [ChatController::class, 'getChat'])->name('chat.get');
Route::get('/chat/users', [ChatController::class, 'getUsers'])->name('chat.users');
Route::post('/chat/add', [ChatController::class, 'addChat'])->name('chat.add');


Route::get('/chat/check-new-messages', [ChatController::class, 'checkNewMessages']);

Route::get('/checknew', [ChatController::class, 'checkNewMessages1']);


// Route::get('/changelang/{lang?}', [MembersController::class, 'ChangeLang'])->name('ChangeLanguge')->middleware(LanguageMiddleware::class);


Route::get('/storemanager', [AppDetailsConroller::class, 'StoreManager'])->name('storemanager')->middleware('auth');

Route::post('/storedevice',  [AppDetailsConroller::class, 'StoreDevice'])->name('storedevice')->middleware('auth');


Route::get('/packages', [PackagesControler::class, 'PackagesView'])->name('packagesview')->middleware('auth');

Route::post('/storepackage',  [PackagesControler::class, 'StorePackage'])->name('storepackage')->middleware('auth');


Route::get('/products', [ProductsControler::class, 'ProductsView'])->name('productsview')->middleware('auth');

Route::post('/storeproduct',  [ProductsControler::class, 'StoreProduct'])->name('storeproduct')->middleware('auth');


Route::get('/logs', [LogsController::class, 'AttendaceView'])->name('attendanceview')->middleware('auth');
Route::get('/alerts', [LogsController::class, 'AlertsView'])->name('alertsview')->middleware('auth');
Route::get('/agperformance', [AccountController::class, 'AgentPerformance'])->name('agentperformance')->middleware('auth');
Route::post('/agstoreper', [AccountController::class, 'AgentStorePer'])->name('agentstorePer')->middleware('auth');
Route::get('/performacedl/{proid}', [AccountController::class, 'Deletecperform'])->name('Deleteperform')->middleware('auth');


use App\Mail\WelcomeMail;
use Illuminate\Support\Facades\Mail;
use App\Models\User;

Route::get('/send-test-email', function () {
    $user = User::first(); // or any test user

    Mail::to('<EMAIL>')->send(new WelcomeMail($user));

    return 'Email sent!';
});

Route::post('/mobile/export', [ExcelController::class, 'exportMobile'])->name('exportmobile')->middleware('auth');
Route::post('/adsl/export', [ExcelController::class, 'exportADSL'])->name('exportadsl')->middleware('auth');
Route::post('/landline/export', [ExcelController::class, 'exportLandline'])->name('exportlandline')->middleware('auth');
Route::post('/devices/export', [ExcelController::class, 'exportDevices'])->name('exportdevices')->middleware('auth');



